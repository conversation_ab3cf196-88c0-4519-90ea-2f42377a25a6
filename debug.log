Watching for file changes with StatReloader
Starting new HTTP connection (1): localhost:8080
http://localhost:8080 "GET /v1/.well-known/ready HTTP/11" 200 0
Starting new HTTP connection (1): localhost:8080
http://localhost:8080 "GET /v1/.well-known/openid-configuration HTTP/11" 404 0
Starting new HTTP connection (1): localhost:8080
http://localhost:8080 "GET /v1/meta HTTP/11" 200 369
load_ssl_context verify=True cert=None trust_env=True http2=False
load_verify_locations cafile='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\certifi\\cacert.pem'
load_ssl_context verify=True cert=None trust_env=True http2=False
load_verify_locations cafile='C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\certifi\\cacert.pem'
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Running: python D:\AI-Agent-Chatbot-main\chatbot\img2.py
Script img2.py timed out: Command '['python', 'D:\\AI-Agent-Chatbot-main\\chatbot\\img2.py']' timed out after 300 seconds
Running: python D:\AI-Agent-Chatbot-main\chatbot\chunking.py
2025-07-02 01:08:34,703 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 01:08:42,479 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 01:08:42,504 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 01:09:08,122 - INFO - chatbot.views - Starting processing pipeline
2025-07-02 01:09:08,122 - INFO - chatbot.views - Running: python D:\AI-Agent-Chatbot-main\chatbot\img2.py
2025-07-02 01:09:10,777 - INFO - Starting summarize_from_db
2025-07-02 01:09:10,777 - INFO - Fetching unsummarized PDF from database
2025-07-02 01:09:11,684 - INFO - Fetched PDF in 0:00:00.906953
2025-07-02 01:09:11,684 - INFO - Processing: GenieNanoSeriesUserManual_images.pdf (id: 3)
2025-07-02 01:09:11,684 - INFO - Extracting pages with images
2025-07-02 01:09:45,899 - ERROR - chatbot.views - Script img2.py failed: 2025-07-02 01:09:10,777 - INFO - Starting summarize_from_db
2025-07-02 01:09:10,777 - INFO - Fetching unsummarized PDF from database
2025-07-02 01:09:11,684 - INFO - Fetched PDF in 0:00:00.906953
2025-07-02 01:09:11,684 - INFO - Processing: GenieNanoSeriesUserManual_images.pdf (id: 3)
2025-07-02 01:09:11,684 - INFO - Extracting pages with images

2025-07-02 01:10:17,814 - INFO - Starting summarize_from_db
2025-07-02 01:10:17,814 - INFO - Fetching unsummarized PDF from database
2025-07-02 01:10:18,078 - INFO - Fetched PDF in 0:00:00.263069
2025-07-02 01:10:18,078 - INFO - Processing: GenieNanoSeriesUserManual_images.pdf (id: 3)
2025-07-02 01:10:18,078 - INFO - Extracting pages with images
2025-07-02 01:11:09,986 - INFO - Extracted 108 pages in 0:00:51.908741
2025-07-02 01:11:10,002 - INFO - Page 1 OCR + GPT summarizing...
2025-07-02 01:11:10,002 - INFO - Starting OCR on image
2025-07-02 01:11:11,041 - INFO - OCR completed in 0:00:01.039301
2025-07-02 01:11:11,041 - INFO - Starting GPT-4o Vision summarization
2025-07-02 01:11:11,041 - INFO - Loading prompt from D:\AI-Agent-Chatbot-main\chatbot\prompt.json
2025-07-02 01:11:11,041 - INFO - Prompt loaded successfully in 0:00:00
2025-07-02 01:11:39,929 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-02 01:11:39,929 - INFO - GPT-4o Vision summarization completed in 0:00:28.887985
2025-07-02 01:11:39,945 - INFO - Page 1 processed in 0:00:29.942930
2025-07-02 01:11:39,945 - INFO - Page 2 OCR + GPT summarizing...
2025-07-02 01:11:39,947 - INFO - Starting OCR on image
2025-07-02 01:11:41,944 - INFO - OCR completed in 0:00:01.997181
2025-07-02 01:11:41,944 - INFO - Starting GPT-4o Vision summarization
2025-07-02 01:11:41,971 - INFO - Loading prompt from D:\AI-Agent-Chatbot-main\chatbot\prompt.json
2025-07-02 01:11:41,971 - INFO - Prompt loaded successfully in 0:00:00
2025-07-02 01:12:09,353 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-02 01:12:09,355 - INFO - GPT-4o Vision summarization completed in 0:00:27.411487
2025-07-02 01:12:09,356 - INFO - Page 2 processed in 0:00:29.409727
2025-07-02 01:12:09,357 - INFO - Page 3 OCR + GPT summarizing...
2025-07-02 01:12:09,357 - INFO - Starting OCR on image
2025-07-02 01:12:10,284 - INFO - OCR completed in 0:00:00.927484
2025-07-02 01:12:10,284 - INFO - Starting GPT-4o Vision summarization
2025-07-02 01:12:10,297 - INFO - Loading prompt from D:\AI-Agent-Chatbot-main\chatbot\prompt.json
2025-07-02 01:12:10,298 - INFO - Prompt loaded successfully in 0:00:00.001169
2025-07-02 01:12:35,246 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-02 01:12:35,248 - INFO - GPT-4o Vision summarization completed in 0:00:24.963961
2025-07-02 01:12:35,250 - INFO - Page 3 processed in 0:00:25.893363
2025-07-02 01:12:35,250 - INFO - Page 4 OCR + GPT summarizing...
2025-07-02 01:12:35,251 - INFO - Starting OCR on image
2025-07-02 01:12:36,204 - INFO - OCR completed in 0:00:00.952969
2025-07-02 01:12:36,205 - INFO - Starting GPT-4o Vision summarization
2025-07-02 01:12:36,211 - INFO - Loading prompt from D:\AI-Agent-Chatbot-main\chatbot\prompt.json
2025-07-02 01:12:36,212 - INFO - Prompt loaded successfully in 0:00:00.000959
2025-07-02 01:13:07,846 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-02 01:13:07,850 - INFO - GPT-4o Vision summarization completed in 0:00:31.644748
2025-07-02 01:13:07,852 - INFO - Page 4 processed in 0:00:32.600630
2025-07-02 01:13:07,853 - INFO - Page 5 OCR + GPT summarizing...
2025-07-02 01:13:07,853 - INFO - Starting OCR on image
2025-07-02 01:13:10,091 - INFO - OCR completed in 0:00:02.238147
2025-07-02 01:13:10,092 - INFO - Starting GPT-4o Vision summarization
2025-07-02 01:13:10,104 - INFO - Loading prompt from D:\AI-Agent-Chatbot-main\chatbot\prompt.json
2025-07-02 01:13:10,106 - INFO - Prompt loaded successfully in 0:00:00.001309
2025-07-02 01:13:37,164 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-02 01:13:37,179 - INFO - GPT-4o Vision summarization completed in 0:00:27.085906
2025-07-02 01:13:37,179 - INFO - Page 5 processed in 0:00:29.326053
2025-07-02 01:13:37,179 - INFO - Page 6 OCR + GPT summarizing...
2025-07-02 01:13:37,179 - INFO - Starting OCR on image
2025-07-02 01:13:38,148 - INFO - OCR completed in 0:00:00.965432
2025-07-02 01:13:38,148 - INFO - Starting GPT-4o Vision summarization
2025-07-02 01:13:38,165 - INFO - Loading prompt from D:\AI-Agent-Chatbot-main\chatbot\prompt.json
2025-07-02 01:13:38,165 - INFO - Prompt loaded successfully in 0:00:00
2025-07-02 01:14:02,797 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-02 01:14:02,799 - INFO - GPT-4o Vision summarization completed in 0:00:24.651011
2025-07-02 01:14:02,800 - INFO - Page 6 processed in 0:00:25.621001
2025-07-02 01:14:02,801 - INFO - Page 7 OCR + GPT summarizing...
2025-07-02 01:14:02,802 - INFO - Starting OCR on image
2025-07-02 01:14:04,593 - INFO - OCR completed in 0:00:01.790994
2025-07-02 01:14:04,593 - INFO - Starting GPT-4o Vision summarization
2025-07-02 01:14:04,607 - INFO - Loading prompt from D:\AI-Agent-Chatbot-main\chatbot\prompt.json
2025-07-02 01:14:04,608 - INFO - Prompt loaded successfully in 0:00:00.000538
2025-07-02 01:14:29,873 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-02 01:14:29,889 - INFO - GPT-4o Vision summarization completed in 0:00:25.296044
2025-07-02 01:14:29,889 - INFO - Page 7 processed in 0:00:27.087038
2025-07-02 01:14:29,889 - INFO - Page 8 OCR + GPT summarizing...
2025-07-02 01:14:29,889 - INFO - Starting OCR on image
2025-07-02 01:17:05,617 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 01:17:14,461 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 01:17:14,489 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 01:17:14,504 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 01:17:38,849 - INFO - Starting summarize_from_db
2025-07-02 01:17:38,850 - INFO - Fetching unsummarized PDF from database
2025-07-02 01:17:39,298 - INFO - Fetched PDF in 0:00:00.447627
2025-07-02 01:17:39,298 - INFO - Processing: GenieNanoSeriesUserManual_images.pdf (id: 3)
2025-07-02 01:17:39,298 - INFO - Extracting pages with images
2025-07-02 01:19:37,145 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 01:19:45,875 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 01:19:45,922 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 01:19:45,953 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 01:25:24,954 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 01:25:32,353 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 01:25:32,394 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 01:25:32,405 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 01:33:41,814 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 01:33:49,432 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 01:33:49,453 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 01:33:49,463 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 02:31:27,646 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 02:31:37,052 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 02:31:37,071 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 02:31:37,087 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 02:33:18,243 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 02:33:26,579 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 02:33:26,599 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 02:33:26,608 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 11:40:03,013 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 11:40:16,811 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:40:16,830 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:41:26,637 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:41:26,663 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:44:18,960 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 11:44:34,468 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 11:46:12,804 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 11:46:22,980 - WARNING - django.request - Not Found: /main.9b97f48c34b9d7b80bd0.hot-update.json
2025-07-02 11:46:23,619 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:46:23,636 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:46:23,670 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:46:23,695 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:46:33,636 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:46:33,678 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:48:09,961 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 11:48:21,218 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:48:21,230 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:48:34,219 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:48:34,269 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:48:50,738 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:48:50,764 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:51:30,395 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 11:51:37,728 - WARNING - django.request - Not Found: /main.17f928b7771512e04b69.hot-update.json
2025-07-02 11:51:38,344 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:51:38,370 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:51:38,385 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:51:38,400 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:53:19,584 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 11:53:26,306 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:53:26,322 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:53:28,588 - WARNING - django.request - Not Found: /main.49d35a100028cd0c1c24.hot-update.json
2025-07-02 11:53:28,887 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:53:28,907 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:53:29,594 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:53:29,612 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:53:29,639 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:53:29,657 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:53:31,102 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:53:31,118 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:53:32,203 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:53:32,232 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:53:33,035 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:53:33,054 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:56:32,336 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 11:57:00,319 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 11:57:10,704 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 11:57:10,735 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 11:57:10,768 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 11:57:10,938 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:57:10,961 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:57:10,984 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 11:57:11,004 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 12:02:33,306 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-02 12:02:33,341 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 12:03:30,530 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 12:03:38,968 - WARNING - django.request - Not Found: /main.9d05b1ca0ae5801d9a71.hot-update.json
2025-07-02 12:04:25,694 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 12:04:36,520 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 12:04:36,575 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 12:04:36,609 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 12:05:08,440 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 12:05:08,478 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 12:09:17,836 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-02 12:09:27,807 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 12:09:27,858 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 12:09:27,899 - WARNING - django.request - Not Found: /favicon.ico
2025-07-02 12:09:28,061 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 12:09:28,070 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 12:09:28,085 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 12:09:28,085 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 12:09:28,105 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-02 12:09:28,105 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-02 12:09:28,121 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-02 12:09:28,123 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-02 12:09:35,861 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 12:09:35,895 - WARNING - django.request - Not Found: /api/prompt/
2025-07-02 23:54:35,143 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 00:10:59,775 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 00:19:59,468 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 00:21:14,045 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 00:23:59,960 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 00:28:23,458 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 00:31:22,133 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 00:44:39,795 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 00:48:38,194 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 00:51:27,642 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:09:26,049 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:12:45,237 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:13:37,389 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:14:11,321 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:14:12,037 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:14:15,470 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:14:16,205 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:15:31,096 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:15:31,911 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:15:36,476 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:15:37,186 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:15:44,939 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:15:45,653 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:16:50,708 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:16:51,496 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:16:54,969 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:16:55,714 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:16:59,297 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:17:00,066 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:17:02,623 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:17:03,348 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:17:05,773 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:17:06,452 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:17:11,986 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:17:12,688 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:17:18,450 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:17:19,115 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:17:20,650 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:17:21,469 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:17:23,833 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:17:24,607 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:18:12,112 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:18:12,842 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:18:25,431 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:18:26,197 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:18:29,269 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:18:30,056 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:18:49,499 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:18:50,311 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:18:57,748 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:18:58,561 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:19:02,770 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:19:03,554 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:19:11,935 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:19:12,699 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:19:33,258 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:19:34,031 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:21:21,760 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:21:22,533 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:21:26,647 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:21:27,403 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:21:30,640 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:21:31,504 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:21:37,914 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:21:44,445 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 01:21:44,460 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 01:21:57,063 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 01:21:57,071 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 01:25:55,060 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 01:25:55,070 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 01:29:08,405 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:29:09,242 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:29:33,615 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:29:40,343 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 01:29:40,354 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 01:29:40,361 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 01:29:40,368 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 01:30:50,635 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 01:30:51,456 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:31:19,987 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:31:26,303 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 01:31:26,314 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 01:31:26,321 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 01:31:26,328 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 01:31:26,338 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 01:31:26,342 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 01:31:26,345 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 01:31:26,351 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 01:31:34,298 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 01:31:34,305 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 01:52:46,391 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 01:52:53,401 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 01:52:53,418 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 01:53:28,633 - WARNING - django.request - Unauthorized: /api/token/
2025-07-03 01:57:15,954 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\upload.py changed, reloading.
2025-07-03 01:57:16,779 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 02:37:30,974 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 02:37:37,691 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 02:37:37,713 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 02:37:37,726 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 02:41:26,318 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 02:41:27,330 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 02:41:31,478 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 02:41:32,250 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 02:41:48,648 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 02:41:55,091 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 02:41:55,112 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 02:41:55,123 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 02:44:20,235 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 02:44:28,347 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 02:44:28,374 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 02:48:52,360 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 02:48:53,226 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 02:48:58,366 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 02:49:16,572 - ERROR - django.request - Internal Server Error: /api/tickets/process_uploads/
2025-07-03 02:54:38,720 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 02:54:45,471 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 02:54:45,484 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 02:55:03,110 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 02:55:09,171 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 02:55:09,190 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 02:55:17,854 - ERROR - django.request - Internal Server Error: /api/tickets/process_uploads/
2025-07-03 02:59:50,904 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 02:59:51,790 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 03:00:08,523 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 03:00:14,774 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 03:00:21,113 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 03:00:21,134 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 03:00:32,530 - ERROR - django.request - Internal Server Error: /api/tickets/process_uploads/
2025-07-03 03:03:30,575 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 03:03:36,997 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 03:03:37,024 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 03:03:48,752 - ERROR - django.request - Internal Server Error: /api/tickets/process_uploads/
2025-07-03 03:05:47,494 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 03:06:00,442 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 03:06:06,959 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 03:06:06,975 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 03:08:11,317 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 03:08:18,113 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 03:08:18,136 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 03:12:24,279 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 03:12:30,426 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 03:12:30,448 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 03:13:57,884 - WARNING - django.request - Not Found: /api/tickets/escalated/
2025-07-03 03:13:57,901 - WARNING - django.request - Not Found: /api/tickets/escalated/
2025-07-03 03:30:52,632 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 03:30:59,273 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 03:30:59,295 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 03:31:13,769 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 03:31:13,778 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 03:35:42,705 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 03:35:42,716 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 03:38:37,762 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 03:38:37,773 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 03:52:12,095 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 03:52:12,104 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 03:52:21,210 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 03:52:21,223 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 03:53:26,855 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 03:53:26,868 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 03:53:29,695 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 03:53:29,704 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:15:56,884 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 04:16:05,451 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 04:16:05,480 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 04:18:13,270 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 04:18:20,233 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 04:18:20,258 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 04:18:30,395 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:18:30,409 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:23:43,658 - WARNING - django.request - Unauthorized: /api/chat/
2025-07-03 04:23:54,131 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:23:54,148 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:29:52,409 - WARNING - django.request - Unauthorized: /api/chat/
2025-07-03 04:30:26,227 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 04:30:32,502 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 04:30:32,518 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 04:30:32,529 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 04:30:32,786 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:30:32,796 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:30:32,803 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:30:32,810 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:30:32,822 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 04:30:32,826 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 04:30:32,829 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 04:30:32,834 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 04:30:38,201 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:30:38,212 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:40:59,873 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 04:41:06,097 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 04:41:06,122 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 04:41:08,361 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:41:08,369 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:44:36,301 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 04:44:37,126 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 04:44:59,928 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 04:45:00,829 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 04:45:04,461 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 04:45:05,170 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 04:45:11,204 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 04:45:17,171 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 04:45:17,189 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 04:45:17,200 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 04:45:17,474 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:45:17,483 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:45:17,491 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:45:17,497 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:45:22,854 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:45:22,862 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:50:38,653 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 04:50:39,499 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 04:50:43,236 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 04:50:43,928 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 04:50:50,723 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 04:50:51,421 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 04:51:04,481 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 04:51:11,201 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 04:51:11,224 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 04:51:14,819 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:51:14,827 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:54:18,138 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 04:54:18,971 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 04:54:40,619 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 04:54:41,404 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 04:54:42,939 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 04:54:43,653 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 04:54:52,415 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 04:54:59,098 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 04:54:59,120 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 04:55:02,221 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 04:55:02,230 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:00:49,536 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 05:00:50,349 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:01:08,684 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 05:01:09,474 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:01:11,999 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 05:01:12,732 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:01:21,670 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:01:28,092 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:01:28,107 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:01:30,162 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:01:30,170 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:03:08,086 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 05:03:08,974 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:03:27,194 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:03:33,120 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:03:33,138 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:03:35,251 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:03:35,260 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:06:28,236 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:06:35,158 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:06:35,185 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:06:37,424 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:06:37,435 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:09:09,173 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 05:09:10,027 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:09:23,828 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:09:30,178 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:09:30,195 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:09:32,343 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:09:32,352 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:14:35,635 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 05:14:36,591 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:16:00,319 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 05:16:01,188 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:16:32,705 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 05:16:33,519 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:16:37,107 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 05:16:37,834 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:16:40,389 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 05:16:41,103 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:16:58,674 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:17:06,770 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:17:06,795 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:17:09,089 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:17:09,096 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:24:06,466 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 05:24:07,240 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:24:09,790 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 05:24:10,484 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:24:14,726 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 05:24:15,566 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:25:12,010 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:25:19,178 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:25:19,196 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:25:38,645 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:25:38,659 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:36:38,416 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:37:06,027 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:38:35,681 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:38:42,405 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:38:42,426 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:38:44,991 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:38:44,998 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:41:56,766 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:41:56,781 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:47:19,049 - WARNING - django.request - Unauthorized: /api/chat/
2025-07-03 05:47:35,512 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:47:35,528 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:48:57,615 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:52:59,586 - WARNING - django.request - Unauthorized: /api/chat/
2025-07-03 05:53:48,910 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 05:53:56,634 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:53:56,667 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:53:58,938 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:53:58,944 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 05:54:53,463 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 05:59:24,794 - WARNING - django.request - Unauthorized: /api/chat/
2025-07-03 06:00:10,185 - WARNING - django.request - Unauthorized: /api/chat/
2025-07-03 06:03:49,195 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 06:03:50,196 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 06:03:51,824 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-03 06:03:52,515 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 06:04:04,037 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 06:04:10,597 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 06:04:10,620 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 06:04:13,772 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 06:04:13,947 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 06:07:48,868 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 06:07:48,878 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 06:13:05,194 - WARNING - django.request - Unauthorized: /api/chat/
2025-07-03 06:13:18,500 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 06:13:18,518 - WARNING - django.request - Not Found: /api/prompt/
2025-07-03 06:18:20,018 - WARNING - django.request - Unauthorized: /api/chat/
2025-07-03 06:18:35,186 - WARNING - django.request - Unauthorized: /api/chat/
2025-07-03 23:50:32,446 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-03 23:50:39,846 - WARNING - django.request - Unauthorized: /api/token/
2025-07-03 23:50:54,387 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 23:55:41,997 - WARNING - django.request - Not Found: /favicon.ico
2025-07-03 23:56:18,988 - WARNING - django.request - Unauthorized: /api/chat/
2025-07-03 23:56:40,482 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,485 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:40,489 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,496 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:40,525 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,533 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:40,543 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,552 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:40,565 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,583 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:40,607 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,609 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:40,635 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,637 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:40,651 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,654 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:40,683 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,685 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:40,714 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,716 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:40,750 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,752 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:40,779 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,781 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:40,808 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,810 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:40,839 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,841 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:40,871 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,874 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:40,906 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,908 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:40,938 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,939 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:40,972 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:40,973 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,004 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,006 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,037 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,038 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,068 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,070 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,103 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,104 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,132 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,135 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,165 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,167 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,195 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,197 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,226 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,228 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,258 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,260 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,290 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,291 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,322 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,324 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,356 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,358 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,387 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,389 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,422 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,423 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,457 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,459 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,493 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,495 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,525 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,528 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,561 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,563 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,595 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,597 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,630 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,632 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,661 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,663 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,695 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,696 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,728 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,730 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,762 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,763 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,795 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,797 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,835 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,836 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,869 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,871 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,907 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,909 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,939 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,941 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:41,973 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:41,974 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,006 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,008 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,043 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,046 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,075 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,077 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,115 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,118 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,158 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,160 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,197 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,199 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,229 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,231 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,270 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,271 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,302 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,304 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,336 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,337 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,364 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,367 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,401 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,402 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,430 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,432 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,461 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,462 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,493 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,495 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,526 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,528 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,557 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,558 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,586 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,591 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,619 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,622 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,648 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,655 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,678 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,692 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,695 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,723 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,724 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,757 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,758 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,790 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,791 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,822 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,824 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,853 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,855 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,884 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,885 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,913 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,915 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,946 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,947 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:42,983 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:42,985 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,013 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,014 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,050 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,052 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,083 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,085 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,117 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,120 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,152 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,154 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,183 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,186 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,217 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,219 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,251 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,252 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,281 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,283 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,313 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,315 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,344 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,346 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,377 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,378 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,408 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,410 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,438 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,439 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,470 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,472 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,504 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,506 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,541 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,543 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,570 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,572 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,604 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,605 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,637 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,638 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,659 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,660 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,671 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,673 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,684 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,686 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,692 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,693 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,697 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,698 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,702 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,703 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,715 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,717 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,721 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,722 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,726 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,727 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,731 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,732 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,735 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,737 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,740 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,741 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,744 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,745 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,751 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,752 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,755 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,756 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,760 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,761 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,764 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,766 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,770 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,771 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,774 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,775 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,779 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,780 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,783 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,785 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,788 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,789 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,793 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,794 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,797 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,799 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,802 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,804 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,807 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,808 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,812 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,813 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,817 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,818 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,821 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,822 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,826 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,827 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,830 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,832 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,835 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,836 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,840 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,841 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,844 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,845 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,849 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,851 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,854 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,855 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,858 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,859 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,863 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,864 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,868 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,869 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,872 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,873 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,876 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,878 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,882 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,883 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,886 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,887 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,891 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,892 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,895 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,896 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,900 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,901 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,905 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,906 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,909 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,910 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,913 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,915 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,919 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,920 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,923 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,924 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,927 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,928 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,932 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,934 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,942 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,951 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,959 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,960 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,976 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,979 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,980 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,985 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,987 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,993 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:43,994 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:43,997 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,001 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,002 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,005 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,006 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,009 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,010 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,014 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,015 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,019 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,020 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,023 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,024 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,027 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,028 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,033 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,034 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,038 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,039 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,043 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,044 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,047 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,048 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,052 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,053 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,057 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,058 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,061 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,062 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,066 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,067 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,070 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,071 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,074 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,076 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,080 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,082 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,086 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,087 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,090 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,091 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,095 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,096 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,100 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,102 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,105 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,106 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,110 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,111 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,115 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,116 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,120 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,121 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,124 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,125 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,128 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,130 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,133 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,135 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,138 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,139 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,143 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,144 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,147 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,148 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,152 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,153 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,156 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-03 23:56:44,157 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-03 23:56:44,161 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:02:21,196 - WARNING - django.request - Unauthorized: /api/files/Genie-Nano-10GSeries-camera-manual.pdf/
2025-07-04 00:05:54,421 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 00:06:04,808 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 00:06:26,243 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 00:06:33,537 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,537 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:33,542 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,543 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:33,588 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,592 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:33,597 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,600 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:33,614 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,622 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:33,632 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,633 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:33,662 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,671 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:33,677 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,682 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:33,703 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,725 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:33,731 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,739 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:33,760 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,782 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:33,790 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,808 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:33,817 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,828 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:33,851 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,855 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:33,883 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,887 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:33,912 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,915 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:33,940 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,942 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:33,975 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:33,977 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,006 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,008 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,032 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,033 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,060 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,061 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,092 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,095 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,126 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,128 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,156 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,157 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,193 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,194 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,220 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,222 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,248 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,249 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,276 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,279 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,306 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,308 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,334 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,336 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,367 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,368 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,397 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,400 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,425 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,426 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,450 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,452 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,480 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,481 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,510 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,512 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,541 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,543 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,570 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,572 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,604 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,606 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,636 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,638 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,670 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,672 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,698 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,700 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,734 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,736 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,764 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,766 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,801 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,804 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,833 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,835 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,884 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,886 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,914 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,916 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,946 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,949 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:34,977 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:34,979 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,012 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,013 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,040 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,041 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,059 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,061 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,069 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,071 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,079 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,082 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,088 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,090 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,093 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,095 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,099 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,100 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,103 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,106 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,107 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,111 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,113 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,117 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,118 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,121 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,123 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,126 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,127 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,133 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,134 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,138 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,139 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,142 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,143 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,146 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,149 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,152 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,153 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,157 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,161 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,162 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,166 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,167 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,172 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,172 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,177 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,178 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,182 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,185 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,186 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,190 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,192 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,196 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,197 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,201 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,202 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,206 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,207 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,212 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,213 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,215 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,219 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,220 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,223 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,226 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,229 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,230 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,233 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,236 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,237 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,240 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,243 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,246 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,248 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,251 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,252 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,255 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,258 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,261 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,262 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,266 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,267 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,271 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,272 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,277 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,278 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,281 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,282 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,286 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,287 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,292 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,293 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,296 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,299 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,300 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:06:35,304 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:06:35,305 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:10,729 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:10,732 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:10,735 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:10,736 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:10,765 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:10,783 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:10,785 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:10,793 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:10,795 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:10,820 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:10,821 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:10,846 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:10,856 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:10,880 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:10,882 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:10,913 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:10,916 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:10,946 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:10,947 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:10,978 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:10,980 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,016 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,017 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,045 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,046 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,075 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,077 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,104 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,106 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,141 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,143 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,175 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,177 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,210 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,212 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,241 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,243 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,271 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,272 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,304 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,306 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,336 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,338 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,369 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,371 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,404 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,405 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,436 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,438 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,464 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,466 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,492 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,494 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,525 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,526 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,554 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,556 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,579 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,581 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,609 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,612 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,640 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,642 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,669 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,670 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,693 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,695 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,726 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,727 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,753 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,755 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,783 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,784 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,815 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,816 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,844 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,846 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,876 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,878 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,904 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,905 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,936 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,938 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,965 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,968 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:11,996 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:11,998 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,027 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,029 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,057 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,059 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,077 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,078 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,111 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,113 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,142 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,144 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,176 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,178 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,203 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,205 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,223 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,225 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,237 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,239 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,252 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,254 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,268 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,269 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,278 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,279 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,288 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,289 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,296 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,298 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,302 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,311 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,313 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,320 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,321 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,330 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,331 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,340 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,342 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,348 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,349 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,358 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,359 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,366 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,367 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,376 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,378 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,384 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,385 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,394 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,395 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,401 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,402 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,412 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,413 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,421 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,422 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,429 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,430 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,438 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,439 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,447 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,448 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,457 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,459 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,465 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,466 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,474 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,476 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,482 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,483 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,491 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,492 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,498 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,499 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,509 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,510 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,517 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,518 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,527 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,528 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,533 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,535 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,544 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,545 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,555 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,556 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,563 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,564 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,571 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,572 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,578 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,579 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,588 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,589 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,596 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,597 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,605 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,607 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,613 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,615 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,623 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,625 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,632 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,633 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:16:12,642 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 00:16:12,643 - WARNING - django.request - Unauthorized: /api/pending_tickets/
2025-07-04 00:17:29,101 - WARNING - urllib3.connectionpool - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002CB74789690>: Failed to resolve 'api.openai.com' ([Errno 11001] getaddrinfo failed)")': /v1/chat/completions
2025-07-04 00:17:41,156 - WARNING - urllib3.connectionpool - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002CB74288110>: Failed to resolve 'api.openai.com' ([Errno 11001] getaddrinfo failed)")': /v1/chat/completions
2025-07-04 00:17:41,160 - ERROR - django.request - Internal Server Error: /api/add_problem_description/
2025-07-04 00:33:55,726 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 00:34:01,980 - WARNING - django.request - Not Found: /main.65916a25bd31b6edfb21.hot-update.json
2025-07-04 00:34:02,524 - WARNING - django.request - Unauthorized: /api/start_ticket_session/TCKT-1SOWX5T0/
2025-07-04 00:34:02,529 - WARNING - django.request - Unauthorized: /api/start_ticket_session/TCKT-1SOWX5T0/
2025-07-04 00:40:50,558 - WARNING - django.request - Not Found: /favicon.ico
2025-07-04 00:41:08,857 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 00:48:23,291 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 00:52:05,245 - WARNING - django.request - Not Found: /main.99f12ba6b6c92ae81f22.hot-update.json
2025-07-04 00:52:07,214 - WARNING - django.request - Not Found: /main.99f12ba6b6c92ae81f22.hot-update.json
2025-07-04 00:54:13,407 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-04 00:54:14,457 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 00:54:30,648 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-04 00:54:31,545 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 00:57:55,483 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 00:57:55,495 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 00:58:04,573 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 00:58:04,583 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 00:58:07,739 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 01:07:47,725 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\models.py changed, reloading.
2025-07-04 01:07:48,181 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\models.py changed, reloading.
2025-07-04 01:07:49,010 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 01:07:49,152 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 01:08:13,926 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-04 01:08:13,928 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-04 01:08:14,870 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 01:08:14,871 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 01:08:31,117 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-04 01:08:31,149 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-04 01:08:32,022 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 01:08:32,043 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 01:10:38,112 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:10:38,126 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:11:36,009 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:11:36,017 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:12:43,199 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 01:16:40,911 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:16:40,924 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:17:19,410 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:17:19,430 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:44:36,921 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 01:45:02,697 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:45:02,710 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:46:24,122 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:46:24,130 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:46:44,761 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:46:44,770 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:48:51,515 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:48:51,524 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:52:27,525 - WARNING - django.request - Unauthorized: /api/chat/
2025-07-04 01:54:30,935 - WARNING - django.request - Unauthorized: /api/chat/
2025-07-04 01:57:50,783 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:57:50,793 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 01:59:35,567 - WARNING - urllib3.connectionpool - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000254FDAE9990>: Failed to resolve 'api.openai.com' ([Errno 11001] getaddrinfo failed)")': /v1/embeddings
2025-07-04 01:59:35,568 - WARNING - urllib3.connectionpool - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000254FDB23690>: Failed to resolve 'api.openai.com' ([Errno 11001] getaddrinfo failed)")': /v1/embeddings
2025-07-04 02:06:58,509 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:06:58,518 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:07:00,725 - WARNING - urllib3.connectionpool - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000254FD532BD0>: Failed to resolve 'api.openai.com' ([Errno 11001] getaddrinfo failed)")': /v1/embeddings
2025-07-04 02:07:00,726 - WARNING - urllib3.connectionpool - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000254FD5AC810>: Failed to resolve 'api.openai.com' ([Errno 11001] getaddrinfo failed)")': /v1/embeddings
2025-07-04 02:07:29,804 - WARNING - urllib3.connectionpool - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000254FD5CF450>: Failed to resolve 'api.openai.com' ([Errno 11001] getaddrinfo failed)")': /v1/embeddings
2025-07-04 02:07:29,805 - WARNING - urllib3.connectionpool - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000254FD57B5D0>: Failed to resolve 'api.openai.com' ([Errno 11001] getaddrinfo failed)")': /v1/embeddings
2025-07-04 02:09:23,166 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:09:23,178 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:10:39,425 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:10:39,438 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:12:33,895 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:12:51,206 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:15:09,051 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-04 02:15:10,125 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 02:15:57,344 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:15:57,352 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:16:21,321 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 02:16:51,362 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:16:51,369 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:17:56,901 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 02:17:56,911 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:18:12,007 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:18:12,017 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:20:23,608 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:20:23,621 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:26:29,214 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:26:29,217 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-04 02:28:15,708 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:28:15,717 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:32:33,371 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-04 02:32:33,397 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-04 02:32:34,270 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 02:32:34,849 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 02:32:59,118 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:32:59,128 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:35:40,512 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-04 02:35:41,156 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-04 02:35:41,525 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 02:35:42,096 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 02:35:52,524 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-04 02:35:53,560 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 02:37:27,296 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 02:38:34,800 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 02:38:56,668 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:38:56,676 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:39:13,876 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:39:13,883 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:40:17,236 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 02:40:29,977 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:40:29,985 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:43:41,373 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 02:43:51,850 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:43:51,858 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:49:29,052 - WARNING - django.request - Unauthorized: /api/chat/
2025-07-04 02:50:05,365 - WARNING - django.request - Unauthorized: /api/chat/
2025-07-04 02:55:07,911 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:55:07,920 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:56:59,734 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\ai_chatbot_backend\settings.py changed, reloading.
2025-07-04 02:57:00,637 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 02:57:54,034 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\ai_chatbot_backend\settings.py changed, reloading.
2025-07-04 02:57:54,889 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 02:57:58,898 - INFO - django.utils.autoreload - C:\Users\<USER>\Desktop\AI-Agent-Chatbot-main\ai_chatbot_backend\settings.py changed, reloading.
2025-07-04 02:57:59,669 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 02:58:10,434 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-04 02:58:26,450 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 02:58:26,459 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 03:01:18,892 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 03:01:18,901 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 03:01:53,860 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 03:01:53,871 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 03:05:05,806 - WARNING - django.request - Not Found: /api/prompt/
2025-07-04 03:05:05,815 - WARNING - django.request - Not Found: /api/prompt/
2025-07-05 00:12:59,366 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 00:13:12,023 - WARNING - django.request - Not Found: /api/prompt/
2025-07-05 00:13:12,043 - WARNING - django.request - Not Found: /api/prompt/
2025-07-05 00:13:22,022 - WARNING - django.request - Unauthorized: /api/prompts/
2025-07-05 00:13:22,028 - WARNING - django.request - Unauthorized: /api/prompts/
2025-07-05 00:14:37,103 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 00:14:48,065 - WARNING - django.request - Unauthorized: /api/prompts/
2025-07-05 00:14:48,068 - WARNING - django.request - Unauthorized: /api/prompts/
2025-07-05 00:20:42,223 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 00:22:36,035 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 11:49:55,770 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 11:50:48,601 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 11:51:30,943 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 11:51:37,195 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 11:51:42,638 - WARNING - django.request - Not Found: /favicon.ico
2025-07-05 12:48:29,279 - WARNING - django.request - Not Found: /api/tickets/escalated/
2025-07-05 12:48:29,292 - WARNING - django.request - Not Found: /api/tickets/escalated/
2025-07-05 14:25:36,081 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 14:29:46,400 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 14:30:24,262 - WARNING - django.request - Not Found: /main.ff505b79c0b19bb1e6ad.hot-update.json
2025-07-05 14:30:26,242 - WARNING - django.request - Not Found: /main.ff505b79c0b19bb1e6ad.hot-update.json
2025-07-05 14:30:28,279 - WARNING - django.request - Not Found: /main.ff505b79c0b19bb1e6ad.hot-update.json
2025-07-05 14:33:25,994 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 14:33:34,693 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 14:35:44,652 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 14:35:53,788 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 14:37:43,621 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 14:37:49,191 - WARNING - django.request - Not Found: /favicon.ico
2025-07-05 14:38:19,809 - ERROR - django.request - Internal Server Error: /api/tickets/process_uploads/
Traceback (most recent call last):
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\chatbot\views.py", line 1457, in run_processing_pipeline
    result = subprocess.run(
             ^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\subprocess.py", line 548, in run
    with Popen(*popenargs, **kwargs) as process:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\subprocess.py", line 1538, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 2] The system cannot find the file specified
2025-07-05 14:40:06,318 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-05 14:40:06,910 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 14:40:36,693 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 14:53:30,036 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 14:53:47,265 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 14:53:51,719 - WARNING - django.request - Not Found: /main.843e4b4d91acd066deda.hot-update.json
2025-07-05 15:04:32,257 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 15:04:37,228 - WARNING - django.request - Not Found: /main.bd4b42c14d09643b24ad.hot-update.json
2025-07-05 15:52:07,419 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 15:52:15,442 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 15:52:21,789 - WARNING - django.request - Not Found: /favicon.ico
2025-07-05 15:54:15,252 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-05 15:54:19,819 - WARNING - django.request - Not Found: /favicon.ico
2025-07-09 13:40:18,503 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-09 13:41:36,854 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-09 13:41:55,727 - WARNING - django.request - Not Found: /favicon.ico
2025-07-12 10:54:54,632 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 10:56:06,536 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 10:56:16,413 - WARNING - django.request - Not Found: /favicon.ico
2025-07-12 10:56:16,438 - WARNING - django.request - Not Found: /favicon.ico
2025-07-12 10:56:28,764 - ERROR - django.request - Internal Server Error: /api/prompts/
2025-07-12 10:56:28,775 - ERROR - django.request - Internal Server Error: /api/prompts/
2025-07-12 10:57:29,927 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-12 10:57:30,917 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 10:58:01,842 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 10:58:09,224 - WARNING - django.request - Not Found: /favicon.ico
2025-07-12 10:59:56,858 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 11:01:26,739 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 11:04:09,791 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 11:08:23,160 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 11:26:13,342 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 11:26:46,082 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 11:27:15,837 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 11:28:17,798 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 11:28:44,664 - WARNING - django.request - Not Found: /main.62f47cb6edaf644cc356.hot-update.json
2025-07-12 11:31:05,639 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-12 11:31:06,470 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 11:32:00,025 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 11:32:58,189 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 11:33:28,678 - WARNING - django.request - Not Found: /main.a4c9b9bd71352bf61bee.hot-update.json
2025-07-12 11:33:30,664 - WARNING - django.request - Not Found: /main.a4c9b9bd71352bf61bee.hot-update.json
2025-07-12 11:44:39,647 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 11:44:46,704 - WARNING - django.request - Not Found: /main.a4c9b9bd71352bf61bee.hot-update.json
2025-07-12 11:44:46,722 - WARNING - django.request - Not Found: /main.a4c9b9bd71352bf61bee.hot-update.json
2025-07-12 11:46:50,940 - WARNING - django.request - Not Found: /main.a4c9b9bd71352bf61bee.hot-update.json
2025-07-12 11:47:38,389 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 11:47:54,417 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 11:48:01,139 - WARNING - django.request - Not Found: /main.718a72eb5a2523055cf2.hot-update.json
2025-07-12 11:48:01,160 - WARNING - django.request - Not Found: /main.718a72eb5a2523055cf2.hot-update.json
2025-07-12 11:48:01,195 - WARNING - django.request - Not Found: /main.718a72eb5a2523055cf2.hot-update.json
2025-07-12 11:48:01,235 - WARNING - django.request - Not Found: /main.718a72eb5a2523055cf2.hot-update.json
2025-07-12 11:51:18,291 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 11:56:02,212 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 11:56:11,534 - WARNING - django.request - Not Found: /favicon.ico
2025-07-12 12:00:38,610 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-12 12:00:39,494 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:01:17,642 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:01:26,769 - WARNING - django.request - Not Found: /favicon.ico
2025-07-12 12:06:06,393 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-12 12:06:06,656 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-12 12:06:07,183 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:06:07,377 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:06:25,149 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-12 12:06:25,196 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-12 12:06:26,179 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:06:26,185 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:06:29,467 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-12 12:06:29,492 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-12 12:06:30,271 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:06:30,281 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:06:53,323 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:07:00,104 - WARNING - django.request - Not Found: /main.a20ca52f113e83dad799.hot-update.json
2025-07-12 12:07:42,038 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:07:49,698 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:07:56,916 - WARNING - django.request - Not Found: /main.37af1936dc511d3dc5ba.hot-update.json
2025-07-12 12:07:58,629 - WARNING - django.request - Not Found: /main.37af1936dc511d3dc5ba.hot-update.json
2025-07-12 12:10:43,568 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:10:50,040 - WARNING - django.request - Not Found: /main.8c6b45d1020369e198d8.hot-update.json
2025-07-12 12:10:50,061 - WARNING - django.request - Not Found: /main.8c6b45d1020369e198d8.hot-update.json
2025-07-12 12:10:50,074 - WARNING - django.request - Not Found: /main.8c6b45d1020369e198d8.hot-update.json
2025-07-12 12:16:45,556 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\models.py changed, reloading.
2025-07-12 12:16:46,333 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:16:52,730 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\models.py changed, reloading.
2025-07-12 12:16:53,349 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:17:06,346 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\urls.py changed, reloading.
2025-07-12 12:17:07,131 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:17:15,781 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\urls.py changed, reloading.
2025-07-12 12:17:16,561 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:17:34,776 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\openai_usage_tracker.py changed, reloading.
2025-07-12 12:17:35,556 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:17:56,832 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\openai_usage_tracker.py changed, reloading.
2025-07-12 12:17:57,621 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:18:18,812 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\openai_usage_tracker.py changed, reloading.
2025-07-12 12:18:19,563 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:18:46,135 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-12 12:18:46,917 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:20:33,087 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\openai_usage_tracker.py changed, reloading.
2025-07-12 12:20:33,850 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:22:30,554 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-12 12:25:18,707 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-14 23:05:49,006 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-14 23:05:59,019 - WARNING - django.request - Not Found: /favicon.ico
2025-07-14 23:06:03,393 - ERROR - django.request - Internal Server Error: /api/pending_tickets/
Traceback (most recent call last):
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\mysql\base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
MySQLdb.OperationalError: (1054, "Unknown column 'chatbot_supportticket.problem_categories' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\chatbot\views.py", line 119, in pending_tickets
    for t in open_tickets:
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\query.py", line 384, in __iter__
    self._fetch_all()
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\mysql\base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
django.db.utils.OperationalError: (1054, "Unknown column 'chatbot_supportticket.problem_categories' in 'field list'")
2025-07-14 23:06:03,488 - ERROR - django.request - Internal Server Error: /api/pending_tickets/
Traceback (most recent call last):
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\mysql\base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
MySQLdb.OperationalError: (1054, "Unknown column 'chatbot_supportticket.problem_categories' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\chatbot\views.py", line 119, in pending_tickets
    for t in open_tickets:
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\query.py", line 384, in __iter__
    self._fetch_all()
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\mysql\base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
django.db.utils.OperationalError: (1054, "Unknown column 'chatbot_supportticket.problem_categories' in 'field list'")
2025-07-14 23:06:38,033 - ERROR - django.request - Internal Server Error: /api/create_ticket/
Traceback (most recent call last):
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\mysql\base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
MySQLdb.OperationalError: (1054, "Unknown column 'problem_categories' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\chatbot\views.py", line 524, in create_ticket
    ticket = serializer.save(user=request.user)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\serializers.py", line 210, in save
    self.instance = self.create(validated_data)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\serializers.py", line 991, in create
    instance = ModelClass._default_manager.create(**validated_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\query.py", line 663, in create
    obj.save(force_insert=True, using=self.db)
  File "D:\AI-Agent-Chatbot-main\chatbot\models.py", line 303, in save
    super().save(*args, **kwargs)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\base.py", line 902, in save
    self.save_base(
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\base.py", line 1008, in save_base
    updated = self._save_table(
              ^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\base.py", line 1169, in _save_table
    results = self._do_insert(
              ^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\base.py", line 1210, in _do_insert
    return manager._insert(
           ^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\query.py", line 1868, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\sql\compiler.py", line 1882, in execute_sql
    cursor.execute(sql, params)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\mysql\base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
django.db.utils.OperationalError: (1054, "Unknown column 'problem_categories' in 'field list'")
2025-07-14 23:11:04,914 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-14 23:11:46,063 - ERROR - django.request - Internal Server Error: /api/create_ticket/
Traceback (most recent call last):
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\mysql\base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
MySQLdb.OperationalError: (1054, "Unknown column 'problem_categories' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\chatbot\views.py", line 524, in create_ticket
    ticket = serializer.save(user=request.user)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\serializers.py", line 210, in save
    self.instance = self.create(validated_data)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\rest_framework\serializers.py", line 991, in create
    instance = ModelClass._default_manager.create(**validated_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\query.py", line 663, in create
    obj.save(force_insert=True, using=self.db)
  File "D:\AI-Agent-Chatbot-main\chatbot\models.py", line 303, in save
    super().save(*args, **kwargs)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\base.py", line 902, in save
    self.save_base(
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\base.py", line 1008, in save_base
    updated = self._save_table(
              ^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\base.py", line 1169, in _save_table
    results = self._do_insert(
              ^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\base.py", line 1210, in _do_insert
    return manager._insert(
           ^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\query.py", line 1868, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\models\sql\compiler.py", line 1882, in execute_sql
    cursor.execute(sql, params)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\django\db\backends\mysql\base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
django.db.utils.OperationalError: (1054, "Unknown column 'problem_categories' in 'field list'")
2025-07-14 23:12:46,894 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\migrations\0019_remove_ticketproblemcategory_category_and_more.py changed, reloading.
2025-07-14 23:12:47,975 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-14 23:22:53,206 - WARNING - django.request - Not Found: /api/problem_categories/
2025-07-14 23:22:55,249 - WARNING - django.request - Unauthorized: /api/create_ticket/
2025-07-14 23:23:01,770 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-14 23:24:55,485 - WARNING - django.request - Not Found: /api/login/
2025-07-14 23:25:21,127 - ERROR - django.request - Internal Server Error: /api/create_ticket/
Traceback (most recent call last):
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\backends\mysql\base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\ProgramData\anaconda3\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
MySQLdb.IntegrityError: (1364, "Field 'product_name' doesn't have a default value")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\ProgramData\anaconda3\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\ProgramData\anaconda3\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI-Agent-Chatbot-main\chatbot\views.py", line 524, in create_ticket
    ticket = serializer.save(user=request.user)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\rest_framework\serializers.py", line 210, in save
    self.instance = self.create(validated_data)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\rest_framework\serializers.py", line 991, in create
    instance = ModelClass._default_manager.create(**validated_data)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\models\query.py", line 663, in create
    obj.save(force_insert=True, using=self.db)
  File "D:\AI-Agent-Chatbot-main\chatbot\models.py", line 303, in save
    super().save(*args, **kwargs)
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\models\base.py", line 902, in save
    self.save_base(
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\models\base.py", line 1008, in save_base
    updated = self._save_table(
              ^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\models\base.py", line 1169, in _save_table
    results = self._do_insert(
              ^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\models\base.py", line 1210, in _do_insert
    return manager._insert(
           ^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\models\query.py", line 1864, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\models\sql\compiler.py", line 1882, in execute_sql
    cursor.execute(sql, params)
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\django\db\backends\mysql\base.py", line 76, in execute
    return self.cursor.execute(query, args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\ProgramData\anaconda3\Lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\ProgramData\anaconda3\Lib\site-packages\MySQLdb\connections.py", line 280, in query
    _mysql.connection.query(self, query)
django.db.utils.IntegrityError: (1364, "Field 'product_name' doesn't have a default value")
2025-07-14 23:27:39,557 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\openai_usage_tracker.py changed, reloading.
2025-07-14 23:27:40,543 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-14 23:27:53,357 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\openai_usage_tracker.py changed, reloading.
2025-07-14 23:27:54,464 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 11:20:15,641 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 11:20:23,201 - WARNING - django.request - Not Found: /favicon.ico
2025-07-19 11:20:23,490 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-19 11:20:23,496 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-19 11:34:54,759 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 11:35:02,506 - WARNING - django.request - Not Found: /favicon.ico
2025-07-19 11:35:02,794 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-19 11:35:02,798 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-19 13:16:04,795 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 13:31:57,018 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 13:31:57,860 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 13:32:13,002 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 13:32:13,830 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 13:36:11,115 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 13:36:11,909 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 13:38:10,883 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 13:44:29,304 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\usage_views.py changed, reloading.
2025-07-19 13:44:30,063 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 13:53:08,060 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 13:53:14,301 - WARNING - django.request - Not Found: /main.503422650532dcfa178b.hot-update.json
2025-07-19 13:56:06,916 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 13:56:12,091 - WARNING - django.request - Not Found: /main.664392f6367f0f091209.hot-update.json
2025-07-19 13:56:12,124 - WARNING - django.request - Not Found: /main.664392f6367f0f091209.hot-update.json
2025-07-19 14:17:09,245 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 14:17:16,760 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 14:25:22,880 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 14:41:26,420 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 14:45:46,864 - WARNING - django.request - Not Found: /api/files/User_Guide-Hints-jan25.pdf/
2025-07-19 14:45:47,144 - WARNING - django.request - Not Found: /favicon.ico
2025-07-19 14:46:40,187 - WARNING - django.request - Not Found: /api/files/User_Guide-Hints-jan25.pdf/
2025-07-19 15:04:42,397 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:12:11,420 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 15:12:12,779 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:12:16,930 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 15:12:17,967 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:12:25,685 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 15:12:26,845 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:12:28,679 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 15:12:29,616 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:12:36,071 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 15:12:37,282 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:12:56,723 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:15:30,451 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 15:15:31,488 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:15:44,397 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:21:16,006 - INFO - openai - error_code=None error_message="This model's maximum context length is 8192 tokens, however you requested 8562 tokens (8562 in your prompt; 0 for the completion). Please reduce your prompt; or completion length." error_param=None error_type=invalid_request_error message='OpenAI API error received' stream_error=False
2025-07-19 15:22:08,947 - INFO - openai - error_code=None error_message="This model's maximum context length is 8192 tokens, however you requested 10684 tokens (10684 in your prompt; 0 for the completion). Please reduce your prompt; or completion length." error_param=None error_type=invalid_request_error message='OpenAI API error received' stream_error=False
2025-07-19 15:25:17,566 - INFO - openai - error_code=None error_message="This model's maximum context length is 8192 tokens, however you requested 12992 tokens (12992 in your prompt; 0 for the completion). Please reduce your prompt; or completion length." error_param=None error_type=invalid_request_error message='OpenAI API error received' stream_error=False
2025-07-19 15:41:07,388 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 15:41:08,953 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:41:16,750 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:42:36,360 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 15:42:38,013 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:43:20,870 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 15:43:21,954 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:43:25,808 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 15:43:26,744 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:43:40,995 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 15:43:42,098 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:43:49,755 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:49:48,785 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 15:49:50,139 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:51:15,005 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 15:51:16,108 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:53:12,858 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 15:53:13,912 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:53:24,729 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:54:53,477 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 15:54:54,730 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:55:08,829 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 15:56:56,408 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 15:56:57,884 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 16:09:26,667 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 16:09:28,216 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 16:09:37,163 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 16:51:48,078 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 16:51:48,972 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 16:51:50,561 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 16:51:51,197 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 16:51:53,809 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 16:51:54,412 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 16:51:57,058 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 16:51:57,692 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 16:52:06,630 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 16:52:07,283 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 16:52:12,012 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 16:52:12,609 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 16:52:16,257 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-19 16:52:16,903 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 16:52:20,263 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-19 16:52:28,232 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-24 19:35:14,815 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-24 19:37:14,261 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-24 19:37:20,732 - WARNING - django.request - Not Found: /favicon.ico
2025-07-24 19:48:02,918 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-24 19:48:04,289 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-24 19:48:11,043 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-25 21:04:06,605 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-25 21:04:13,273 - WARNING - django.request - Not Found: /favicon.ico
2025-07-25 21:04:13,579 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-25 21:04:13,582 - WARNING - django.request - Unauthorized: /api/user_info/
2025-07-26 09:27:03,447 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 09:27:08,645 - WARNING - django.request - Not Found: /favicon.ico
2025-07-26 09:37:10,441 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\models.py changed, reloading.
2025-07-26 09:37:11,601 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 09:37:38,702 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 09:37:39,402 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 09:52:29,147 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 10:01:19,333 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 10:01:24,783 - WARNING - django.request - Not Found: /favicon.ico
2025-07-26 10:03:59,692 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 10:06:10,937 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 11:42:42,306 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 11:42:50,089 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 11:49:47,906 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 11:54:52,652 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 12:05:52,938 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 12:05:53,497 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 12:05:59,878 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 12:06:00,495 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 12:06:03,428 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 12:06:15,171 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 12:08:27,044 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 12:08:27,833 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 12:08:30,087 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 12:08:30,644 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 12:14:31,349 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 12:14:31,898 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 12:14:37,231 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 12:14:37,824 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 12:14:43,708 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 12:17:09,014 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 12:17:09,554 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 12:17:11,713 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 12:17:12,239 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:26:15,645 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:28:05,634 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:28:11,547 - WARNING - django.request - Not Found: /favicon.ico
2025-07-26 14:48:08,086 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 14:48:08,959 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:48:27,557 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 14:48:28,166 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:48:42,294 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 14:48:42,941 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:49:05,678 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 14:49:06,278 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:49:17,256 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 14:49:17,866 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:49:29,894 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\urls.py changed, reloading.
2025-07-26 14:49:30,482 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:49:46,737 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 14:49:47,376 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:51:33,866 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:51:42,976 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:54:47,838 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 14:54:48,444 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:54:50,753 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 14:54:51,321 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:54:57,804 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 14:54:58,612 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:55:01,050 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 14:55:01,687 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:55:05,995 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:56:23,311 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 14:56:24,150 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:56:27,578 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 14:56:28,191 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:58:52,665 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 14:58:53,454 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 14:59:03,088 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 15:29:09,447 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 15:29:14,386 - WARNING - django.request - Not Found: /main.56b035b17f365d71abb3.hot-update.json
2025-07-26 15:29:14,393 - WARNING - django.request - Not Found: /main.56b035b17f365d71abb3.hot-update.json
2025-07-26 15:29:44,331 - WARNING - django.request - Not Found: /main.56b035b17f365d71abb3.hot-update.json
2025-07-26 15:57:51,305 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 15:57:57,264 - WARNING - django.request - Not Found: /favicon.ico
2025-07-26 16:05:38,089 - INFO - openai - error_code=None error_message="This model's maximum context length is 8192 tokens, however you requested 9821 tokens (9821 in your prompt; 0 for the completion). Please reduce your prompt; or completion length." error_param=None error_type=invalid_request_error message='OpenAI API error received' stream_error=False
2025-07-26 16:06:10,079 - INFO - openai - error_code=None error_message="This model's maximum context length is 8192 tokens, however you requested 9816 tokens (9816 in your prompt; 0 for the completion). Please reduce your prompt; or completion length." error_param=None error_type=invalid_request_error message='OpenAI API error received' stream_error=False
2025-07-26 16:08:35,546 - INFO - openai - error_code=None error_message="This model's maximum context length is 8192 tokens, however you requested 9850 tokens (9850 in your prompt; 0 for the completion). Please reduce your prompt; or completion length." error_param=None error_type=invalid_request_error message='OpenAI API error received' stream_error=False
2025-07-26 16:09:07,749 - INFO - openai - error_code=None error_message="This model's maximum context length is 8192 tokens, however you requested 15296 tokens (15296 in your prompt; 0 for the completion). Please reduce your prompt; or completion length." error_param=None error_type=invalid_request_error message='OpenAI API error received' stream_error=False
2025-07-26 17:01:45,581 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 17:22:03,192 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 17:22:03,799 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 17:22:19,713 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 17:22:20,286 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 17:24:04,950 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 17:24:15,591 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 17:24:20,910 - WARNING - django.request - Not Found: /main.4f5249d97c926de17cc1.hot-update.json
2025-07-26 17:24:21,691 - WARNING - django.request - Not Found: /main.4f5249d97c926de17cc1.hot-update.json
2025-07-26 17:42:52,072 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 17:42:52,671 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 17:43:05,400 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 17:43:05,978 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 17:43:43,055 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 17:43:43,628 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 17:43:54,348 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 17:43:54,914 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 17:47:33,581 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 17:47:43,538 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 17:47:48,906 - WARNING - django.request - Not Found: /main.effef04630957d54aad5.hot-update.json
2025-07-26 17:47:48,926 - WARNING - django.request - Not Found: /main.effef04630957d54aad5.hot-update.json
2025-07-26 17:47:49,748 - WARNING - django.request - Not Found: /main.effef04630957d54aad5.hot-update.json
2025-07-26 17:53:36,517 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 18:00:28,634 - INFO - openai - error_code=None error_message="This model's maximum context length is 8192 tokens, however you requested 8775 tokens (8775 in your prompt; 0 for the completion). Please reduce your prompt; or completion length." error_param=None error_type=invalid_request_error message='OpenAI API error received' stream_error=False
2025-07-26 18:00:52,594 - INFO - openai - error_code=None error_message="This model's maximum context length is 8192 tokens, however you requested 8773 tokens (8773 in your prompt; 0 for the completion). Please reduce your prompt; or completion length." error_param=None error_type=invalid_request_error message='OpenAI API error received' stream_error=False
2025-07-26 18:01:38,982 - INFO - openai - error_code=None error_message="This model's maximum context length is 8192 tokens, however you requested 8807 tokens (8807 in your prompt; 0 for the completion). Please reduce your prompt; or completion length." error_param=None error_type=invalid_request_error message='OpenAI API error received' stream_error=False
2025-07-26 18:04:28,045 - INFO - openai - error_code=None error_message="This model's maximum context length is 8192 tokens, however you requested 8845 tokens (8845 in your prompt; 0 for the completion). Please reduce your prompt; or completion length." error_param=None error_type=invalid_request_error message='OpenAI API error received' stream_error=False
2025-07-26 18:08:06,998 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 18:08:08,278 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 18:08:11,210 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-07-26 18:08:12,258 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 18:08:24,588 - INFO - openai - error_code=None error_message="This model's maximum context length is 8192 tokens, however you requested 8879 tokens (8879 in your prompt; 0 for the completion). Please reduce your prompt; or completion length." error_param=None error_type=invalid_request_error message='OpenAI API error received' stream_error=False
2025-07-26 18:08:42,874 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-07-26 18:14:33,121 - INFO - openai - error_code=None error_message="This model's maximum context length is 8192 tokens, however you requested 8929 tokens (8929 in your prompt; 0 for the completion). Please reduce your prompt; or completion length." error_param=None error_type=invalid_request_error message='OpenAI API error received' stream_error=False
2025-07-26 18:15:09,156 - INFO - openai - error_code=None error_message="This model's maximum context length is 8192 tokens, however you requested 8930 tokens (8930 in your prompt; 0 for the completion). Please reduce your prompt; or completion length." error_param=None error_type=invalid_request_error message='OpenAI API error received' stream_error=False
2025-07-26 18:15:22,170 - INFO - openai - error_code=None error_message="This model's maximum context length is 8192 tokens, however you requested 8971 tokens (8971 in your prompt; 0 for the completion). Please reduce your prompt; or completion length." error_param=None error_type=invalid_request_error message='OpenAI API error received' stream_error=False
2025-08-04 02:48:13,852 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-04 02:48:32,568 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-04 02:48:42,945 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-04 02:49:28,069 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-04 02:50:21,003 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-08 20:13:35,374 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-08 20:17:15,225 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-08 20:17:52,633 - ERROR - django.request - Internal Server Error: /api/tickets/process_uploads/
2025-08-08 21:04:40,135 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\openai_usage_tracker.py changed, reloading.
2025-08-08 21:04:41,140 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-08 21:06:50,187 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-08 21:08:26,038 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-08 21:08:26,937 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-08 21:09:39,706 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-08 21:15:14,060 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-08 21:15:20,396 - WARNING - django.request - Not Found: /favicon.ico
2025-08-08 21:15:20,417 - WARNING - django.request - Not Found: /favicon.ico
2025-08-08 21:15:45,724 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-08 21:15:51,190 - WARNING - django.request - Not Found: /favicon.ico
2025-08-08 21:15:51,205 - WARNING - django.request - Not Found: /favicon.ico
2025-08-08 21:16:05,586 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-08 21:17:23,580 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-08 21:36:24,468 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-08 21:36:25,389 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-08 21:38:16,973 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-08 21:38:42,112 - INFO - chatbot.views - Starting module: chatbot.img2
2025-08-08 21:39:10,600 - INFO - chatbot.views - [chatbot.img2] STDOUT: [PROCESSING] Processing: SaperaGettingStarted_USB_Cameras_removed_images.pdf (id: 1)
2025-08-08 21:39:10,600 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 1 OCR + GPT summarizing...
2025-08-08 21:39:10,605 - INFO - chatbot.views - [chatbot.img2] STDOUT: Summary PDF inserted into pdf_files as 'SaperaGettingStarted_USB_Cameras_removed_images_summary.pdf'
2025-08-08 21:39:10,605 - INFO - chatbot.views - [chatbot.img2] STDOUT: [SUCCESS] Completed summarization for: SaperaGettingStarted_USB_Cameras_removed_images.pdf
2025-08-08 21:39:10,605 - INFO - chatbot.views - [chatbot.img2] STDOUT: [SUCCESS] All image PDFs have been summarized.
2025-08-08 21:39:10,732 - INFO - chatbot.views - Module chatbot.img2 completed successfully
2025-08-08 21:39:10,733 - INFO - chatbot.views - Starting module: chatbot.chunking
2025-08-08 21:39:12,631 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Processing SaperaGettingStarted_USB_Cameras_removed.pdf (Camera Type: area_scan)...
2025-08-08 21:39:12,632 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Processing SaperaGettingStarted_USB_Cameras_removed_images_summary.pdf (Camera Type: None)...
2025-08-08 21:39:12,632 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Inserted 6 new chunk(s).
2025-08-08 21:39:12,821 - INFO - chatbot.views - Module chatbot.chunking completed successfully
2025-08-08 21:39:12,821 - INFO - chatbot.views - Starting module: chatbot.vector_embedding
2025-08-08 21:39:14,551 - ERROR - chatbot.views - [chatbot.vector_embedding] STDERR: D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\weaviate\data\crud_data.py:823: DeprecationWarning: Weaviate Server version >= 1.14.x STRONGLY recommends using class namespaced APIs, please specify the `class_name` argument for this. The non-class namespaced APIs (None value for `class_name`) are going to be removed in the future versions of the Weaviate Server and Weaviate Python Client.
2025-08-08 21:39:14,552 - ERROR - chatbot.views - [chatbot.vector_embedding] STDERR:   warnings.warn(
2025-08-08 21:39:17,712 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Created class 'AreaScanChunks'.
2025-08-08 21:39:17,714 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Created class 'LineScanChunks'.
2025-08-08 21:39:17,715 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Processing 6 chunks in batches of 1...
2025-08-08 21:39:17,715 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 1 from SaperaGettingStarted_USB_Cameras_removed.pdf to AreaScanChunks
2025-08-08 21:39:17,716 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 2 from SaperaGettingStarted_USB_Cameras_removed.pdf to AreaScanChunks
2025-08-08 21:39:17,716 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 3 from SaperaGettingStarted_USB_Cameras_removed.pdf to AreaScanChunks
2025-08-08 21:39:17,716 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 1 from SaperaGettingStarted_USB_Cameras_removed_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:39:17,717 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 2 from SaperaGettingStarted_USB_Cameras_removed_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:39:17,717 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 3 from SaperaGettingStarted_USB_Cameras_removed_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:39:17,773 - INFO - chatbot.views - Module chatbot.vector_embedding completed successfully
2025-08-08 21:39:17,773 - INFO - chatbot.views - Processing pipeline completed successfully
2025-08-08 21:40:39,977 - INFO - chatbot.views - Starting module: chatbot.img2
2025-08-08 21:43:22,679 - INFO - chatbot.views - [chatbot.img2] STDOUT: [PROCESSING] Processing: Genie-Nano-Camera-Installation-Guide_images.pdf (id: 2)
2025-08-08 21:43:22,681 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 1 OCR + GPT summarizing...
2025-08-08 21:43:22,681 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 2 OCR + GPT summarizing...
2025-08-08 21:43:22,694 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 3 OCR + GPT summarizing...
2025-08-08 21:43:22,694 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 4 OCR + GPT summarizing...
2025-08-08 21:43:22,709 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 5 OCR + GPT summarizing...
2025-08-08 21:43:22,710 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 6 OCR + GPT summarizing...
2025-08-08 21:43:22,710 - INFO - chatbot.views - [chatbot.img2] STDOUT: Summary PDF inserted into pdf_files as 'Genie-Nano-Camera-Installation-Guide_images_summary.pdf'
2025-08-08 21:43:22,713 - INFO - chatbot.views - [chatbot.img2] STDOUT: [SUCCESS] Completed summarization for: Genie-Nano-Camera-Installation-Guide_images.pdf
2025-08-08 21:43:22,716 - INFO - chatbot.views - [chatbot.img2] STDOUT: [SUCCESS] All image PDFs have been summarized.
2025-08-08 21:43:22,837 - INFO - chatbot.views - Module chatbot.img2 completed successfully
2025-08-08 21:43:22,838 - INFO - chatbot.views - Starting module: chatbot.chunking
2025-08-08 21:43:25,389 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Skipping SaperaGettingStarted_USB_Cameras_removed.pdf (no content changes).
2025-08-08 21:43:25,389 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Skipping SaperaGettingStarted_USB_Cameras_removed_images_summary.pdf (no content changes).
2025-08-08 21:43:25,391 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Processing Genie-Nano-Camera-Installation-Guide_images_summary.pdf (Camera Type: None)...
2025-08-08 21:43:25,391 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Processing Genie-Nano-Camera-Installation-Guide.pdf (Camera Type: area_scan)...
2025-08-08 21:43:25,391 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Inserted 34 new chunk(s).
2025-08-08 21:43:25,547 - INFO - chatbot.views - Module chatbot.chunking completed successfully
2025-08-08 21:43:25,548 - INFO - chatbot.views - Starting module: chatbot.vector_embedding
2025-08-08 21:43:26,896 - ERROR - chatbot.views - [chatbot.vector_embedding] STDERR: D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\weaviate\data\crud_data.py:823: DeprecationWarning: Weaviate Server version >= 1.14.x STRONGLY recommends using class namespaced APIs, please specify the `class_name` argument for this. The non-class namespaced APIs (None value for `class_name`) are going to be removed in the future versions of the Weaviate Server and Weaviate Python Client.
2025-08-08 21:43:26,899 - ERROR - chatbot.views - [chatbot.vector_embedding] STDERR:   warnings.warn(
2025-08-08 21:43:46,186 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Processing 34 chunks in batches of 1...
2025-08-08 21:43:46,190 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 1 from Genie-Nano-Camera-Installation-Guide.pdf to AreaScanChunks
2025-08-08 21:43:46,190 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 2 from Genie-Nano-Camera-Installation-Guide.pdf to AreaScanChunks
2025-08-08 21:43:46,190 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 3 from Genie-Nano-Camera-Installation-Guide.pdf to AreaScanChunks
2025-08-08 21:43:46,190 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 4 from Genie-Nano-Camera-Installation-Guide.pdf to AreaScanChunks
2025-08-08 21:43:46,190 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 5 from Genie-Nano-Camera-Installation-Guide.pdf to AreaScanChunks
2025-08-08 21:43:46,190 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 6 from Genie-Nano-Camera-Installation-Guide.pdf to AreaScanChunks
2025-08-08 21:43:46,190 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 7 from Genie-Nano-Camera-Installation-Guide.pdf to AreaScanChunks
2025-08-08 21:43:46,190 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 8 from Genie-Nano-Camera-Installation-Guide.pdf to AreaScanChunks
2025-08-08 21:43:46,190 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 9 from Genie-Nano-Camera-Installation-Guide.pdf to AreaScanChunks
2025-08-08 21:43:46,190 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 10 from Genie-Nano-Camera-Installation-Guide.pdf to AreaScanChunks
2025-08-08 21:43:46,190 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 11 from Genie-Nano-Camera-Installation-Guide.pdf to AreaScanChunks
2025-08-08 21:43:46,190 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 12 from Genie-Nano-Camera-Installation-Guide.pdf to AreaScanChunks
2025-08-08 21:43:46,191 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 13 from Genie-Nano-Camera-Installation-Guide.pdf to AreaScanChunks
2025-08-08 21:43:46,191 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 1 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,191 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 2 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,191 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 3 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,192 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 4 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,192 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 5 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,192 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 6 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,192 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 7 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,192 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 8 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,192 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 9 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,192 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 10 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,194 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 11 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,194 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 12 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,194 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 13 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,195 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 14 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,195 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 15 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,195 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 16 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,195 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 17 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,195 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 18 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,195 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 19 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,195 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 20 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,196 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 21 from Genie-Nano-Camera-Installation-Guide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:43:46,250 - INFO - chatbot.views - Module chatbot.vector_embedding completed successfully
2025-08-08 21:43:46,250 - INFO - chatbot.views - Processing pipeline completed successfully
2025-08-08 21:45:55,514 - INFO - chatbot.views - Starting module: chatbot.img2
2025-08-08 21:52:45,511 - INFO - chatbot.views - [chatbot.img2] STDOUT: [PROCESSING] Processing: Genie-Nano-5GCamera-Installationguide_images.pdf (id: 3)
2025-08-08 21:52:45,512 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 1 OCR + GPT summarizing...
2025-08-08 21:52:45,512 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 2 OCR + GPT summarizing...
2025-08-08 21:52:45,512 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 3 OCR + GPT summarizing...
2025-08-08 21:52:45,512 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 4 OCR + GPT summarizing...
2025-08-08 21:52:45,513 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 5 OCR + GPT summarizing...
2025-08-08 21:52:45,513 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 6 OCR + GPT summarizing...
2025-08-08 21:52:45,513 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 7 OCR + GPT summarizing...
2025-08-08 21:52:45,513 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 8 OCR + GPT summarizing...
2025-08-08 21:52:45,513 - INFO - chatbot.views - [chatbot.img2] STDOUT: Summary PDF inserted into pdf_files as 'Genie-Nano-5GCamera-Installationguide_images_summary.pdf'
2025-08-08 21:52:45,513 - INFO - chatbot.views - [chatbot.img2] STDOUT: [SUCCESS] Completed summarization for: Genie-Nano-5GCamera-Installationguide_images.pdf
2025-08-08 21:52:45,513 - INFO - chatbot.views - [chatbot.img2] STDOUT: [PROCESSING] Processing: Genie-Nano10GCamera-InstallationGuide_images.pdf (id: 4)
2025-08-08 21:52:45,513 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 1 OCR + GPT summarizing...
2025-08-08 21:52:45,513 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 2 OCR + GPT summarizing...
2025-08-08 21:52:45,514 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 3 OCR + GPT summarizing...
2025-08-08 21:52:45,514 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 4 OCR + GPT summarizing...
2025-08-08 21:52:45,514 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 5 OCR + GPT summarizing...
2025-08-08 21:52:45,514 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 6 OCR + GPT summarizing...
2025-08-08 21:52:45,514 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 7 OCR + GPT summarizing...
2025-08-08 21:52:45,514 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 8 OCR + GPT summarizing...
2025-08-08 21:52:45,514 - INFO - chatbot.views - [chatbot.img2] STDOUT: Summary PDF inserted into pdf_files as 'Genie-Nano10GCamera-InstallationGuide_images_summary.pdf'
2025-08-08 21:52:45,515 - INFO - chatbot.views - [chatbot.img2] STDOUT: [SUCCESS] Completed summarization for: Genie-Nano10GCamera-InstallationGuide_images.pdf
2025-08-08 21:52:45,515 - INFO - chatbot.views - [chatbot.img2] STDOUT: [SUCCESS] All image PDFs have been summarized.
2025-08-08 21:52:45,655 - INFO - chatbot.views - Module chatbot.img2 completed successfully
2025-08-08 21:52:45,655 - INFO - chatbot.views - Starting module: chatbot.chunking
2025-08-08 21:52:51,100 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Skipping SaperaGettingStarted_USB_Cameras_removed.pdf (no content changes).
2025-08-08 21:52:51,100 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Skipping SaperaGettingStarted_USB_Cameras_removed_images_summary.pdf (no content changes).
2025-08-08 21:52:51,100 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Skipping Genie-Nano-Camera-Installation-Guide_images_summary.pdf (no content changes).
2025-08-08 21:52:51,100 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Processing Genie-Nano-5GCamera-Installationguide_images_summary.pdf (Camera Type: None)...
2025-08-08 21:52:51,101 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Skipping Genie-Nano-Camera-Installation-Guide.pdf (no content changes).
2025-08-08 21:52:51,101 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Processing Genie-Nano-5GCamera-Installationguide.pdf (Camera Type: area_scan)...
2025-08-08 21:52:51,101 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Processing Genie-Nano10GCamera-InstallationGuide_images_summary.pdf (Camera Type: None)...
2025-08-08 21:52:51,101 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Processing Genie-Nano10GCamera-InstallationGuide.pdf (Camera Type: area_scan)...
2025-08-08 21:52:51,101 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Inserted 76 new chunk(s).
2025-08-08 21:52:51,254 - INFO - chatbot.views - Module chatbot.chunking completed successfully
2025-08-08 21:52:51,254 - INFO - chatbot.views - Starting module: chatbot.vector_embedding
2025-08-08 21:52:52,705 - ERROR - chatbot.views - [chatbot.vector_embedding] STDERR: D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\weaviate\data\crud_data.py:823: DeprecationWarning: Weaviate Server version >= 1.14.x STRONGLY recommends using class namespaced APIs, please specify the `class_name` argument for this. The non-class namespaced APIs (None value for `class_name`) are going to be removed in the future versions of the Weaviate Server and Weaviate Python Client.
2025-08-08 21:52:52,706 - ERROR - chatbot.views - [chatbot.vector_embedding] STDERR:   warnings.warn(
2025-08-08 21:53:38,424 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Processing 76 chunks in batches of 1...
2025-08-08 21:53:38,425 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 1 from Genie-Nano-5GCamera-Installationguide.pdf to AreaScanChunks
2025-08-08 21:53:38,425 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 2 from Genie-Nano-5GCamera-Installationguide.pdf to AreaScanChunks
2025-08-08 21:53:38,426 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 3 from Genie-Nano-5GCamera-Installationguide.pdf to AreaScanChunks
2025-08-08 21:53:38,426 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 4 from Genie-Nano-5GCamera-Installationguide.pdf to AreaScanChunks
2025-08-08 21:53:38,426 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 5 from Genie-Nano-5GCamera-Installationguide.pdf to AreaScanChunks
2025-08-08 21:53:38,427 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 6 from Genie-Nano-5GCamera-Installationguide.pdf to AreaScanChunks
2025-08-08 21:53:38,427 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 7 from Genie-Nano-5GCamera-Installationguide.pdf to AreaScanChunks
2025-08-08 21:53:38,427 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 8 from Genie-Nano-5GCamera-Installationguide.pdf to AreaScanChunks
2025-08-08 21:53:38,428 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 9 from Genie-Nano-5GCamera-Installationguide.pdf to AreaScanChunks
2025-08-08 21:53:38,428 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Chunk already exists in Weaviate: 10 from Genie-Nano-5GCamera-Installationguide.pdf
2025-08-08 21:53:38,428 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Chunk already exists in Weaviate: 11 from Genie-Nano-5GCamera-Installationguide.pdf
2025-08-08 21:53:38,428 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 12 from Genie-Nano-5GCamera-Installationguide.pdf to AreaScanChunks
2025-08-08 21:53:38,429 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 13 from Genie-Nano-5GCamera-Installationguide.pdf to AreaScanChunks
2025-08-08 21:53:38,429 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 14 from Genie-Nano-5GCamera-Installationguide.pdf to AreaScanChunks
2025-08-08 21:53:38,430 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 1 from Genie-Nano10GCamera-InstallationGuide.pdf to AreaScanChunks
2025-08-08 21:53:38,430 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 2 from Genie-Nano10GCamera-InstallationGuide.pdf to AreaScanChunks
2025-08-08 21:53:38,430 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 3 from Genie-Nano10GCamera-InstallationGuide.pdf to AreaScanChunks
2025-08-08 21:53:38,431 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 4 from Genie-Nano10GCamera-InstallationGuide.pdf to AreaScanChunks
2025-08-08 21:53:38,431 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 5 from Genie-Nano10GCamera-InstallationGuide.pdf to AreaScanChunks
2025-08-08 21:53:38,432 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 6 from Genie-Nano10GCamera-InstallationGuide.pdf to AreaScanChunks
2025-08-08 21:53:38,433 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 7 from Genie-Nano10GCamera-InstallationGuide.pdf to AreaScanChunks
2025-08-08 21:53:38,433 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 8 from Genie-Nano10GCamera-InstallationGuide.pdf to AreaScanChunks
2025-08-08 21:53:38,434 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 9 from Genie-Nano10GCamera-InstallationGuide.pdf to AreaScanChunks
2025-08-08 21:53:38,434 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 10 from Genie-Nano10GCamera-InstallationGuide.pdf to AreaScanChunks
2025-08-08 21:53:38,435 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 11 from Genie-Nano10GCamera-InstallationGuide.pdf to AreaScanChunks
2025-08-08 21:53:38,435 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 12 from Genie-Nano10GCamera-InstallationGuide.pdf to AreaScanChunks
2025-08-08 21:53:38,436 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 13 from Genie-Nano10GCamera-InstallationGuide.pdf to AreaScanChunks
2025-08-08 21:53:38,436 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 14 from Genie-Nano10GCamera-InstallationGuide.pdf to AreaScanChunks
2025-08-08 21:53:38,437 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 1 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,437 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 2 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,437 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 3 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,438 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 4 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,438 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 5 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,438 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 6 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,439 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 7 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,439 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 8 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,439 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 9 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,440 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 10 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,440 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 11 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,440 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 12 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,441 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 13 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,441 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 14 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,442 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 15 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,442 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 16 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,442 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 17 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,443 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 18 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,443 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 19 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,443 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 20 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,444 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 21 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,444 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 22 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,444 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 23 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,444 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 24 from Genie-Nano-5GCamera-Installationguide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,445 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 1 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,445 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 2 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,445 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 3 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,445 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 4 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,446 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 5 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,446 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 6 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,446 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 7 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,447 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 8 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,447 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 9 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,447 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 10 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,448 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 11 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,448 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 12 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,448 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 13 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,448 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 14 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,449 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 15 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,449 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 16 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,449 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 17 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,450 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 18 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,450 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 19 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,450 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 20 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,450 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 21 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,450 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 22 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,451 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 23 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,451 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 24 from Genie-Nano10GCamera-InstallationGuide_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 21:53:38,503 - INFO - chatbot.views - Module chatbot.vector_embedding completed successfully
2025-08-08 21:53:38,505 - INFO - chatbot.views - Processing pipeline completed successfully
2025-08-08 21:58:47,394 - INFO - chatbot.views - Starting module: chatbot.img2
2025-08-08 22:48:45,830 - INFO - chatbot.views - [chatbot.img2] STDOUT: [PROCESSING] Processing: Genie-Nano-Series-camera-manual_images.pdf (id: 5)
2025-08-08 22:48:45,832 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 1 OCR + GPT summarizing...
2025-08-08 22:48:45,832 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 2 OCR + GPT summarizing...
2025-08-08 22:48:45,832 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 3 OCR + GPT summarizing...
2025-08-08 22:48:45,833 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 4 OCR + GPT summarizing...
2025-08-08 22:48:45,833 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 5 OCR + GPT summarizing...
2025-08-08 22:48:45,833 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 6 OCR + GPT summarizing...
2025-08-08 22:48:45,833 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 7 OCR + GPT summarizing...
2025-08-08 22:48:45,833 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 8 OCR + GPT summarizing...
2025-08-08 22:48:45,833 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 9 OCR + GPT summarizing...
2025-08-08 22:48:45,835 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 10 OCR + GPT summarizing...
2025-08-08 22:48:45,835 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 11 OCR + GPT summarizing...
2025-08-08 22:48:45,835 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 12 OCR + GPT summarizing...
2025-08-08 22:48:45,835 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 13 OCR + GPT summarizing...
2025-08-08 22:48:45,835 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 14 OCR + GPT summarizing...
2025-08-08 22:48:45,836 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 15 OCR + GPT summarizing...
2025-08-08 22:48:45,836 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 16 OCR + GPT summarizing...
2025-08-08 22:48:45,836 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 17 OCR + GPT summarizing...
2025-08-08 22:48:45,836 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 18 OCR + GPT summarizing...
2025-08-08 22:48:45,837 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 19 OCR + GPT summarizing...
2025-08-08 22:48:45,837 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 20 OCR + GPT summarizing...
2025-08-08 22:48:45,837 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 21 OCR + GPT summarizing...
2025-08-08 22:48:45,837 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 22 OCR + GPT summarizing...
2025-08-08 22:48:45,837 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 23 OCR + GPT summarizing...
2025-08-08 22:48:45,837 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 24 OCR + GPT summarizing...
2025-08-08 22:48:45,837 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 25 OCR + GPT summarizing...
2025-08-08 22:48:45,838 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 26 OCR + GPT summarizing...
2025-08-08 22:48:45,838 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 27 OCR + GPT summarizing...
2025-08-08 22:48:45,838 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 28 OCR + GPT summarizing...
2025-08-08 22:48:45,838 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 29 OCR + GPT summarizing...
2025-08-08 22:48:45,838 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 30 OCR + GPT summarizing...
2025-08-08 22:48:45,838 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 31 OCR + GPT summarizing...
2025-08-08 22:48:45,838 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 32 OCR + GPT summarizing...
2025-08-08 22:48:45,840 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 33 OCR + GPT summarizing...
2025-08-08 22:48:45,840 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 34 OCR + GPT summarizing...
2025-08-08 22:48:45,841 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 35 OCR + GPT summarizing...
2025-08-08 22:48:45,841 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 36 OCR + GPT summarizing...
2025-08-08 22:48:45,841 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 37 OCR + GPT summarizing...
2025-08-08 22:48:45,841 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 38 OCR + GPT summarizing...
2025-08-08 22:48:45,841 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 39 OCR + GPT summarizing...
2025-08-08 22:48:45,842 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 40 OCR + GPT summarizing...
2025-08-08 22:48:45,842 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 41 OCR + GPT summarizing...
2025-08-08 22:48:45,842 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 42 OCR + GPT summarizing...
2025-08-08 22:48:45,842 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 43 OCR + GPT summarizing...
2025-08-08 22:48:45,842 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 44 OCR + GPT summarizing...
2025-08-08 22:48:45,843 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 45 OCR + GPT summarizing...
2025-08-08 22:48:45,843 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 46 OCR + GPT summarizing...
2025-08-08 22:48:45,843 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 47 OCR + GPT summarizing...
2025-08-08 22:48:45,843 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 48 OCR + GPT summarizing...
2025-08-08 22:48:45,843 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 49 OCR + GPT summarizing...
2025-08-08 22:48:45,844 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 50 OCR + GPT summarizing...
2025-08-08 22:48:45,844 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 51 OCR + GPT summarizing...
2025-08-08 22:48:45,844 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 52 OCR + GPT summarizing...
2025-08-08 22:48:45,844 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 53 OCR + GPT summarizing...
2025-08-08 22:48:45,844 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 54 OCR + GPT summarizing...
2025-08-08 22:48:45,844 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 55 OCR + GPT summarizing...
2025-08-08 22:48:45,844 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 56 OCR + GPT summarizing...
2025-08-08 22:48:45,845 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 57 OCR + GPT summarizing...
2025-08-08 22:48:45,845 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 58 OCR + GPT summarizing...
2025-08-08 22:48:45,845 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 59 OCR + GPT summarizing...
2025-08-08 22:48:45,845 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 60 OCR + GPT summarizing...
2025-08-08 22:48:45,845 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 61 OCR + GPT summarizing...
2025-08-08 22:48:45,847 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 62 OCR + GPT summarizing...
2025-08-08 22:48:45,847 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 63 OCR + GPT summarizing...
2025-08-08 22:48:45,847 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 64 OCR + GPT summarizing...
2025-08-08 22:48:45,847 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 65 OCR + GPT summarizing...
2025-08-08 22:48:45,847 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 66 OCR + GPT summarizing...
2025-08-08 22:48:45,848 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 67 OCR + GPT summarizing...
2025-08-08 22:48:45,848 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 68 OCR + GPT summarizing...
2025-08-08 22:48:45,848 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 69 OCR + GPT summarizing...
2025-08-08 22:48:45,848 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 70 OCR + GPT summarizing...
2025-08-08 22:48:45,848 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 71 OCR + GPT summarizing...
2025-08-08 22:48:45,848 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 72 OCR + GPT summarizing...
2025-08-08 22:48:45,849 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 73 OCR + GPT summarizing...
2025-08-08 22:48:45,849 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 74 OCR + GPT summarizing...
2025-08-08 22:48:45,849 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 75 OCR + GPT summarizing...
2025-08-08 22:48:45,849 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 76 OCR + GPT summarizing...
2025-08-08 22:48:45,849 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 77 OCR + GPT summarizing...
2025-08-08 22:48:45,849 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 78 OCR + GPT summarizing...
2025-08-08 22:48:45,850 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 79 OCR + GPT summarizing...
2025-08-08 22:48:45,850 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 80 OCR + GPT summarizing...
2025-08-08 22:48:45,850 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 81 OCR + GPT summarizing...
2025-08-08 22:48:45,850 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 82 OCR + GPT summarizing...
2025-08-08 22:48:45,850 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 83 OCR + GPT summarizing...
2025-08-08 22:48:45,850 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 84 OCR + GPT summarizing...
2025-08-08 22:48:45,850 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 85 OCR + GPT summarizing...
2025-08-08 22:48:45,852 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 86 OCR + GPT summarizing...
2025-08-08 22:48:45,852 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 87 OCR + GPT summarizing...
2025-08-08 22:48:45,852 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 88 OCR + GPT summarizing...
2025-08-08 22:48:45,852 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 89 OCR + GPT summarizing...
2025-08-08 22:48:45,853 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 90 OCR + GPT summarizing...
2025-08-08 22:48:45,853 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 91 OCR + GPT summarizing...
2025-08-08 22:48:45,853 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 92 OCR + GPT summarizing...
2025-08-08 22:48:45,853 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 93 OCR + GPT summarizing...
2025-08-08 22:48:45,853 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 94 OCR + GPT summarizing...
2025-08-08 22:48:45,853 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 95 OCR + GPT summarizing...
2025-08-08 22:48:45,854 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 96 OCR + GPT summarizing...
2025-08-08 22:48:45,854 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 97 OCR + GPT summarizing...
2025-08-08 22:48:45,854 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 98 OCR + GPT summarizing...
2025-08-08 22:48:45,854 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 99 OCR + GPT summarizing...
2025-08-08 22:48:45,854 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 100 OCR + GPT summarizing...
2025-08-08 22:48:45,854 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 101 OCR + GPT summarizing...
2025-08-08 22:48:45,855 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 102 OCR + GPT summarizing...
2025-08-08 22:48:45,855 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 103 OCR + GPT summarizing...
2025-08-08 22:48:45,855 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 104 OCR + GPT summarizing...
2025-08-08 22:48:45,855 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 105 OCR + GPT summarizing...
2025-08-08 22:48:45,856 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 106 OCR + GPT summarizing...
2025-08-08 22:48:45,857 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 107 OCR + GPT summarizing...
2025-08-08 22:48:45,857 - INFO - chatbot.views - [chatbot.img2] STDOUT: [INFO] Page 108 OCR + GPT summarizing...
2025-08-08 22:48:45,857 - INFO - chatbot.views - [chatbot.img2] STDOUT: Summary PDF inserted into pdf_files as 'Genie-Nano-Series-camera-manual_images_summary.pdf'
2025-08-08 22:48:45,857 - INFO - chatbot.views - [chatbot.img2] STDOUT: [SUCCESS] Completed summarization for: Genie-Nano-Series-camera-manual_images.pdf
2025-08-08 22:48:45,857 - INFO - chatbot.views - [chatbot.img2] STDOUT: [SUCCESS] All image PDFs have been summarized.
2025-08-08 22:48:46,066 - INFO - chatbot.views - Module chatbot.img2 completed successfully
2025-08-08 22:48:46,068 - INFO - chatbot.views - Starting module: chatbot.chunking
2025-08-08 22:48:49,024 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Skipping SaperaGettingStarted_USB_Cameras_removed.pdf (no content changes).
2025-08-08 22:48:49,024 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Skipping SaperaGettingStarted_USB_Cameras_removed_images_summary.pdf (no content changes).
2025-08-08 22:48:49,024 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Skipping Genie-Nano-Camera-Installation-Guide_images_summary.pdf (no content changes).
2025-08-08 22:48:49,025 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Skipping Genie-Nano-Camera-Installation-Guide.pdf (no content changes).
2025-08-08 22:48:49,025 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Skipping Genie-Nano-5GCamera-Installationguide.pdf (no content changes).
2025-08-08 22:48:49,025 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Skipping Genie-Nano-5GCamera-Installationguide_images_summary.pdf (no content changes).
2025-08-08 22:48:49,026 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Skipping Genie-Nano10GCamera-InstallationGuide_images_summary.pdf (no content changes).
2025-08-08 22:48:49,026 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Processing Genie-Nano-Series-camera-manual_images_summary.pdf (Camera Type: None)...
2025-08-08 22:48:49,026 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Skipping Genie-Nano10GCamera-InstallationGuide.pdf (no content changes).
2025-08-08 22:48:49,026 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Processing Genie-Nano-Series-camera-manual.pdf (Camera Type: area_scan)...
2025-08-08 22:48:49,027 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: '31.1540.9533'
2025-08-08 22:48:49,028 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: '31.1484.2633'
2025-08-08 22:48:49,028 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: '31.1455.9137'
2025-08-08 22:48:49,029 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: '31.1427.5633'
2025-08-08 22:48:49,029 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: '31.1399.2.72'
2025-08-08 22:48:49,029 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: '31.1370.8733'
2025-08-08 22:48:49,030 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: '31.1342.5.72'
2025-08-08 22:48:49,030 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: '31.1314.1.68'
2025-08-08 22:48:49,030 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: '31.1285.8372'
2025-08-08 22:48:49,043 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: '31.1257.4872'
2025-08-08 22:48:49,059 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: '31.1200.7972'
2025-08-08 22:48:49,060 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: 'Td5'
2025-08-08 22:48:49,075 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: '31.1172.4437'
2025-08-08 22:48:49,090 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: '31.1144.1033'
2025-08-08 22:48:49,090 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: '31.1115.7533'
2025-08-08 22:48:49,091 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: '31.187.4133'
2025-08-08 22:48:49,091 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: '31.159.0633'
2025-08-08 22:48:49,091 - INFO - chatbot.views - [chatbot.chunking] STDOUT: MuPDF error: syntax error: unknown keyword: 'E'
2025-08-08 22:49:14,672 - INFO - chatbot.views - [chatbot.chunking] STDOUT: Inserted 973 new chunk(s).
2025-08-08 22:49:14,877 - INFO - chatbot.views - Module chatbot.chunking completed successfully
2025-08-08 22:49:14,879 - INFO - chatbot.views - Starting module: chatbot.vector_embedding
2025-08-08 22:49:16,636 - ERROR - chatbot.views - [chatbot.vector_embedding] STDERR: D:\AI-Agent-Chatbot-main\myenv\Lib\site-packages\weaviate\data\crud_data.py:823: DeprecationWarning: Weaviate Server version >= 1.14.x STRONGLY recommends using class namespaced APIs, please specify the `class_name` argument for this. The non-class namespaced APIs (None value for `class_name`) are going to be removed in the future versions of the Weaviate Server and Weaviate Python Client.
2025-08-08 22:49:16,637 - ERROR - chatbot.views - [chatbot.vector_embedding] STDERR:   warnings.warn(
2025-08-08 22:51:08,592 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Processing 975 chunks in batches of 1...
2025-08-08 22:51:08,593 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Chunk already exists in Weaviate: 10 from Genie-Nano-5GCamera-Installationguide.pdf
2025-08-08 22:51:08,594 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Chunk already exists in Weaviate: 11 from Genie-Nano-5GCamera-Installationguide.pdf
2025-08-08 22:51:08,594 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 1 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,594 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 2 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,594 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 3 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,596 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 4 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,596 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 5 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,597 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 6 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,597 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 7 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,597 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 8 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,597 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 9 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,598 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 10 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,598 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 11 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,598 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 12 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,598 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 13 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,599 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 14 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,599 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 15 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,599 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 16 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,599 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 17 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,599 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 18 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,600 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 19 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,600 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 20 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,600 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 21 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,600 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 22 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,601 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 23 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,602 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 24 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,602 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 25 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,602 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 26 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,603 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 27 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,603 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 28 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,603 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 29 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,603 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 30 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,603 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 31 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,603 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 32 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,605 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 33 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,605 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 34 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,605 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 35 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,605 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 36 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,605 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 37 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,605 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 38 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,605 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 39 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,605 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 40 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,606 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 41 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,606 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 42 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,606 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 43 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,606 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 44 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,607 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 45 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,608 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 46 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,608 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 47 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,608 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 48 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,608 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 49 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,609 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 50 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,609 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 51 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,610 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 52 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,610 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 53 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,610 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 54 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,610 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 55 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,610 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 56 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,610 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 57 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,610 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 58 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,611 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 59 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,611 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 60 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,611 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 61 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,611 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 62 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,611 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 63 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,611 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 64 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,612 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 65 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,612 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 66 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,612 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 67 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,612 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 68 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,612 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 69 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,612 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 70 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,612 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 71 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,613 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 72 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,613 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 73 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,613 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 74 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,613 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 75 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,613 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 76 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,613 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 77 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,613 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 78 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,614 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 79 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,614 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 80 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,614 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 81 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,614 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 82 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,614 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 83 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,614 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 84 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,614 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 85 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,615 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 86 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,615 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 87 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,615 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 88 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,616 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 89 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,616 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 90 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,616 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 91 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,616 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 92 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,616 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 93 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,616 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 94 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,616 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 95 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,617 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 96 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,617 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 97 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,617 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 98 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:08,617 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 99 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,125 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 100 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,126 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 101 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,127 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 102 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,127 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 103 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,128 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 104 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,128 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 105 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,129 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 106 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,129 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 107 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,131 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 108 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,132 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 109 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,132 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 110 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,133 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 111 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,133 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 112 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,134 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 113 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,134 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 114 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,135 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 115 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,135 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 116 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,136 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 117 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,136 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 118 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,136 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 119 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,138 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 120 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,138 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 121 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,139 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 122 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,139 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 123 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,139 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 124 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,140 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 125 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,140 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 126 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,140 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 127 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,140 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 128 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,140 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 129 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,141 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 130 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,141 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 131 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,141 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 132 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,141 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 133 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,141 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 134 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,142 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 135 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,142 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 136 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,142 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 137 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,142 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 138 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,142 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 139 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,143 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 140 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,143 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 141 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,143 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 142 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,143 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 143 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,144 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 144 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,144 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 145 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,145 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 146 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,145 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 147 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,145 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 148 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,145 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 149 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,146 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 150 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,146 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 151 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,146 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 152 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,147 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 153 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,147 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 154 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,147 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 155 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,147 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 156 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,147 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 157 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,148 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 158 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,148 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 159 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,148 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 160 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,148 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 161 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,149 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 162 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,149 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 163 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,149 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 164 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,149 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 165 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,149 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 166 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,150 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 167 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,150 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 168 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,150 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 169 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,150 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 170 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,150 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 171 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,150 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 172 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,150 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 173 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,152 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 174 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,152 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 175 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,152 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 176 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,152 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 177 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,152 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 178 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,153 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 179 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,153 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 180 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,153 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 181 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,153 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 182 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,153 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 183 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,153 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 184 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,153 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 185 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,154 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 186 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,154 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 187 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,154 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 188 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,154 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 189 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,154 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 190 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,154 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 191 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,154 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 192 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,155 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 193 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,155 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 194 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,155 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 195 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,155 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 196 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,155 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 197 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,155 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 198 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,156 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 199 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:51:59,156 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 200 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,089 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 201 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,091 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 202 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,092 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 203 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,093 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 204 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,102 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 205 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,103 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 206 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,103 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 207 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,103 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 208 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,103 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 209 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,103 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 210 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,104 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 211 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,104 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 212 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,104 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 213 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,104 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 214 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,104 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 215 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,105 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 216 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,105 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 217 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,105 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 218 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,106 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 219 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,106 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 220 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,107 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 221 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,107 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 222 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,107 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 223 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,107 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 224 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,108 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 225 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,108 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 226 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,108 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 227 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,108 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 228 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,109 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 229 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,109 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 230 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,110 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 231 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,110 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 232 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,110 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 233 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,110 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 234 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,111 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 235 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,111 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 236 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,111 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 237 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,112 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 238 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,112 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 239 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,112 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 240 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,112 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 241 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,113 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 242 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,113 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 243 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,113 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 244 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,113 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 245 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,114 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 246 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,114 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 247 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,114 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 248 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,115 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 249 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,115 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 250 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,116 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 251 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,116 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 252 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,116 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 253 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,117 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 254 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,117 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 255 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,117 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 256 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,117 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 257 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,118 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 258 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,119 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 259 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,119 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 260 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,119 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 261 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,119 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 262 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,120 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 263 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,120 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 264 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,120 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 265 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,120 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 266 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,120 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 267 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,121 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 268 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,121 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 269 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,124 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 270 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,124 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 271 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,125 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 272 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,125 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 273 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,125 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 274 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,125 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 275 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,126 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 276 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,126 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 277 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,126 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 278 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,126 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 279 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,127 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 280 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,127 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 281 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,127 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 282 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,127 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 283 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,127 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 284 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,128 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 285 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,128 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 286 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,128 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 287 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,128 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 288 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,129 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 289 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,129 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 290 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,129 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 291 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,129 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 292 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,130 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 293 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,130 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 294 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,130 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 295 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,130 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 296 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,131 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 297 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,131 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 298 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,131 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 299 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,131 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 300 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:52:53,132 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 301 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,539 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 302 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,541 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 303 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,542 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 304 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,542 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 305 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,543 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 306 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,544 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 307 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,544 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 308 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,545 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 309 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,545 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 310 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,546 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 311 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,546 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 312 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,547 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 313 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,547 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 314 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,547 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 315 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,548 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 316 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,548 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 317 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,549 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 318 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,549 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 319 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,550 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 320 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,550 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 321 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,551 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 322 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,551 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 323 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,552 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 324 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,552 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 325 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,553 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 326 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,553 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 327 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,554 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 328 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,554 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 329 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,555 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 330 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,555 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 331 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,555 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 332 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,556 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 333 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,556 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 334 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,556 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 335 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,556 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 336 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,556 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 337 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,558 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 338 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,558 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 339 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,558 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 340 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,558 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 341 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,559 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 342 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,560 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 343 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,560 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 344 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,560 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 345 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,561 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 346 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,562 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 347 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,563 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 348 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,563 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 349 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,564 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 350 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,564 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 351 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,565 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 352 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,565 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 353 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,566 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 354 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,566 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 355 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,567 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 356 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,567 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 357 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,567 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 358 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,569 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 359 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,570 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 360 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,570 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 361 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,571 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 362 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,571 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 363 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,572 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 364 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,572 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 365 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,572 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 366 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,572 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 367 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,572 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 368 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,573 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 369 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,573 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 370 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,573 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 371 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,574 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 372 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,575 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 373 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,575 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 374 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,575 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 375 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,576 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 376 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,576 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 377 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,576 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 378 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,576 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 379 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,577 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 380 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,577 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 381 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,577 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 382 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,577 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 383 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,578 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 384 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,578 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 385 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,579 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 386 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,579 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 387 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,579 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 388 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,579 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 389 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,580 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 390 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,580 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 391 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,580 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 392 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,581 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 393 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,581 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 394 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,581 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 395 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,582 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 396 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,583 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 397 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,583 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 398 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,583 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 399 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,584 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 400 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,584 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 401 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:53:44,584 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 402 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,005 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 403 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,006 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 404 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,008 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 405 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,010 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 406 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,010 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 407 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,010 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 408 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,010 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 409 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,011 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 410 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,011 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 411 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,011 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 412 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,011 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 413 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,011 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 414 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,011 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 415 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,012 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 416 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,012 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 417 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,013 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 418 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,013 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 419 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,013 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 420 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,013 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 421 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,013 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 422 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,013 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 423 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,013 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 424 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,013 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 425 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,013 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 426 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,013 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 427 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,014 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 428 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,014 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 429 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,014 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 430 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,014 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 431 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,014 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 432 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,014 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 433 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,014 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 434 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,014 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 435 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,014 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 436 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,015 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 437 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,015 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 438 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,015 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 439 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,015 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 440 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,015 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 441 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,015 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 442 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,015 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 443 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,017 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 444 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,017 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 445 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,017 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 446 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,017 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 447 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,017 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 448 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,017 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 449 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,018 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 450 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,019 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 451 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,019 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 452 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,019 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 453 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,019 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 454 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,019 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 455 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,019 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 456 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,019 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 457 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,019 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 458 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,020 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 459 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,020 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 460 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,021 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 461 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,021 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 462 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,022 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 463 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,022 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 464 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,023 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 465 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,023 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 466 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,023 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 467 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,024 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 468 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,024 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 469 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,025 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 470 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,025 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 471 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,025 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 472 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,025 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 473 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,025 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 474 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,025 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 475 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,026 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 476 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,026 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 477 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,026 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 478 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,026 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 479 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,026 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 480 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,027 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 481 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,027 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 482 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,027 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 483 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,027 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 484 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,027 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 485 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,027 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 486 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,027 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 487 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,027 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 488 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,027 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 489 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,028 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 490 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,028 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 491 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,028 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 492 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,029 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 493 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,029 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 494 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,029 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 495 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,029 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 496 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,029 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 497 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,029 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 498 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,029 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 499 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,029 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 500 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,030 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 501 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,030 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 502 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:54:40,030 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 503 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,110 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 504 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,111 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 505 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,111 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 506 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,112 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 507 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,112 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 508 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,113 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 509 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,113 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 510 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,114 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 511 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,114 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 512 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,114 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 513 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,115 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 514 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,115 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 515 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,116 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 516 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,116 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 517 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,117 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 518 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,117 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 519 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,118 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 520 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,118 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 521 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,119 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 522 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,119 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 523 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,119 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 524 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,120 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 525 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,120 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 526 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,121 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 527 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,121 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 528 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,122 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 529 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,122 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 530 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,123 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 531 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,123 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 532 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,123 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 533 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,123 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 534 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,126 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 535 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,126 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 536 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,127 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 537 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,127 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 538 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,127 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 539 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,128 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 540 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,128 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 541 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,128 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 542 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,129 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 543 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,129 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 544 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,129 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 545 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,130 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 546 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,130 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 547 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,130 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 548 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,131 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 549 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,131 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 550 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,132 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 551 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,132 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 552 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,133 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 553 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,134 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 554 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,134 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 555 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,134 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 556 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,135 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 557 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,139 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 558 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,139 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 559 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,141 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 560 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,141 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 561 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,142 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 562 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,142 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 563 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,142 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 564 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,142 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 565 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,143 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 566 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,143 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 567 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,143 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 568 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,144 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 569 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,144 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 570 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,144 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 571 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,144 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 572 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,145 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 573 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,145 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 574 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,145 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 575 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,145 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 576 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,146 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 577 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,146 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 578 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,146 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 579 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,146 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 580 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,147 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 581 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,147 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 582 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,148 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 583 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,148 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 584 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,148 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 585 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,149 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 586 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,149 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 587 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,149 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 588 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,149 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 589 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,150 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 590 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,150 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 591 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,150 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 592 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,150 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 593 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,151 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 594 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,151 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 595 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,151 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 596 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,151 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 597 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,152 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 598 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,152 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 599 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,152 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 600 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,153 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 601 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,153 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 602 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,153 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 603 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:55:28,154 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 604 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,546 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 605 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,548 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 606 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,548 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 607 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,548 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 608 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,548 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 609 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,549 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 610 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,549 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 611 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,549 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 612 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,549 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 613 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,550 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 614 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,553 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 615 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,555 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 616 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,555 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 617 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,556 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 618 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,556 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 619 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,558 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 620 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,560 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 621 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,560 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 622 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,560 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 623 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,560 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 624 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,560 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 625 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,562 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 626 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,562 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 627 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,563 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 628 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,563 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 629 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,563 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 630 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,564 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 631 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,564 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 632 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,564 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 633 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,565 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 634 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,566 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 635 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,566 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 636 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,567 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 637 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,567 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 638 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,567 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 639 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,568 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 640 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,569 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 641 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,569 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 642 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,569 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 643 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,569 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 644 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,570 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 645 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,570 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 646 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,570 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 647 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,571 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 648 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,571 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 649 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,571 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 650 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,571 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 651 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,571 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 652 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,571 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 653 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,571 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 654 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,572 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 655 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,572 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 656 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,572 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 657 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,572 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 658 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,572 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 659 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,572 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 660 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,573 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 661 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,573 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 662 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,573 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 663 from Genie-Nano-Series-camera-manual.pdf to AreaScanChunks
2025-08-08 22:56:15,573 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 1 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,573 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 2 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,573 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 3 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,574 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 4 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,574 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 5 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,574 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 6 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,574 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 7 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,574 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 8 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,574 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 9 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,574 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 10 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,575 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 11 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,575 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 12 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,575 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 13 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,575 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 14 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,575 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 15 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,575 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 16 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,575 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 17 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,576 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 18 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,576 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 19 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,576 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 20 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,577 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 21 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,577 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 22 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,577 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 23 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,577 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 24 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,577 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 25 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,577 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 26 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,578 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 27 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,578 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 28 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,578 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 29 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,578 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 30 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,578 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 31 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,578 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 32 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,579 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 33 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:56:15,579 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 34 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,873 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 35 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,874 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 36 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,875 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 37 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,875 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 38 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,876 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 39 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,877 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 40 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,878 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 41 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,879 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 42 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,880 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 43 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,880 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 44 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,881 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 45 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,882 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 46 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,882 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 47 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,882 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 48 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,883 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 49 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,884 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 50 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,885 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 51 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,885 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 52 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,886 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 53 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,886 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 54 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,887 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 55 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,888 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 56 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,888 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 57 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,888 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 58 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,888 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 59 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,889 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 60 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,889 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 61 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,889 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 62 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,889 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 63 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,890 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 64 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,891 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 65 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,891 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 66 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,891 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 67 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,891 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 68 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,892 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 69 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,892 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 70 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,893 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 71 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,893 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 72 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,893 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 73 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,894 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 74 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,894 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 75 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,894 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 76 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,894 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 77 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,894 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 78 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,895 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 79 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,895 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 80 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,895 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 81 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,895 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 82 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,896 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 83 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,896 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 84 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,897 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 85 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,897 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 86 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,898 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 87 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,898 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 88 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,898 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 89 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,899 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 90 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,899 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 91 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,900 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 92 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,900 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 93 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,901 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 94 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,901 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 95 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,902 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 96 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,902 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 97 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,903 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 98 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,903 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 99 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,904 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 100 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,904 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 101 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,904 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 102 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,905 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 103 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,905 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 104 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,905 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 105 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,905 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 106 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,906 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 107 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,906 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 108 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,906 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 109 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,907 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 110 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,907 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 111 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,907 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 112 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,908 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 113 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,908 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 114 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,908 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 115 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,909 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 116 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:02,909 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 117 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,926 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 118 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,927 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 119 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,927 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 120 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,927 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 121 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,928 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 122 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,928 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 123 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,928 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 124 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,930 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 125 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,930 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 126 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,930 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 127 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,931 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 128 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,931 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 129 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,931 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 130 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,932 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 131 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,932 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 132 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,932 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 133 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,932 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 134 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,933 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 135 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,934 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 136 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,934 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 137 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,934 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 138 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,935 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 139 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,935 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 140 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,935 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 141 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,935 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 142 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,935 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 143 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,937 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 144 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,937 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 145 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,937 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 146 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,938 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 147 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,938 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 148 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,938 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 149 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,938 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 150 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,938 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 151 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,939 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 152 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,939 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 153 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,939 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 154 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,939 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 155 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,940 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 156 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,940 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 157 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,940 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 158 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,940 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 159 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,940 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 160 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,941 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 161 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,941 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 162 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,941 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 163 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,941 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 164 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,941 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 165 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,942 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 166 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,942 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 167 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,942 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 168 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,942 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 169 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,942 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 170 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,943 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 171 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,943 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 172 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,943 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 173 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,943 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 174 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,943 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 175 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,944 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 176 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,944 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 177 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,944 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 178 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,944 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 179 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,944 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 180 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,944 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 181 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,944 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 182 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,945 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 183 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,945 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 184 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,945 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 185 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,945 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 186 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,945 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 187 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,945 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 188 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,946 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 189 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,946 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 190 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,946 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 191 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,946 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 192 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,946 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 193 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,946 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 194 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,946 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 195 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,946 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 196 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,947 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 197 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,947 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 198 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:44,947 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 199 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,788 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 200 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,790 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 201 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,791 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 202 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,791 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 203 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,792 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 204 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,792 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 205 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,792 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 206 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,793 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 207 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,793 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 208 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,793 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 209 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,793 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 210 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,793 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 211 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,794 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 212 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,794 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 213 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,794 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 214 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,794 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 215 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,794 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 216 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,795 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 217 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,795 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 218 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,796 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 219 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,796 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 220 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,796 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 221 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,796 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 222 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,797 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 223 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,797 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 224 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,797 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 225 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,797 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 226 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,797 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 227 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,797 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 228 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,797 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 229 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,797 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 230 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,798 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 231 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,798 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 232 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,798 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 233 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,798 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 234 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,798 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 235 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,798 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 236 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,799 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 237 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,799 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 238 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,799 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 239 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,799 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 240 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,799 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 241 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,800 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 242 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,800 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 243 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,800 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 244 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,800 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 245 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,801 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 246 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,801 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 247 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,801 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 248 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,801 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 249 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,802 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 250 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,802 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 251 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,802 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 252 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,802 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 253 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,802 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 254 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,802 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 255 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,803 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 256 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,803 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 257 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,803 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 258 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,803 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 259 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,803 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 260 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,803 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 261 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,803 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 262 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,803 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 263 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,804 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 264 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,804 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 265 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,804 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 266 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,804 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 267 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,804 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 268 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,804 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 269 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,804 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 270 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,805 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 271 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,805 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 272 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,805 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 273 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,805 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 274 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,805 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 275 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,805 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 276 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,805 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 277 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,805 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 278 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,805 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 279 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,806 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 280 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,806 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 281 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,806 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 282 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,806 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 283 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,806 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 284 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,807 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 285 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,807 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 286 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,807 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 287 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,807 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 288 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,807 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 289 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,808 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 290 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,808 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 291 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,808 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 292 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,808 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 293 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,808 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 294 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,808 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 295 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,808 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 296 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,808 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 297 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,808 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 298 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,808 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 299 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,808 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 300 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,809 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 301 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,809 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 302 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,809 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 303 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,809 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 304 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,809 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 305 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,809 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 306 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,809 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 307 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,810 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 308 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,810 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 309 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,810 - INFO - chatbot.views - [chatbot.vector_embedding] STDOUT: Vectorized chunk 310 from Genie-Nano-Series-camera-manual_images_summary.pdf to ChunkEmbeddingsV2
2025-08-08 22:57:58,853 - INFO - chatbot.views - Module chatbot.vector_embedding completed successfully
2025-08-08 22:57:58,854 - INFO - chatbot.views - Processing pipeline completed successfully
2025-08-23 16:25:06,916 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-23 16:25:15,719 - WARNING - django.request - Not Found: /favicon.ico
2025-08-23 16:25:15,753 - WARNING - django.request - Not Found: /favicon.ico
2025-08-23 16:25:15,773 - WARNING - django.request - Not Found: /favicon.ico
2025-08-23 16:31:32,429 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-23 16:31:34,110 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-23 16:31:55,527 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-23 16:32:48,041 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-23 16:32:49,251 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-23 16:34:01,965 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-23 16:34:03,504 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-23 16:36:20,878 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-23 16:36:21,627 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-23 16:36:32,651 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-23 16:39:13,887 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-23 16:39:14,689 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-23 16:45:25,566 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-23 16:45:26,214 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-23 16:52:32,819 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-23 16:59:00,982 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-23 16:59:01,686 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-23 16:59:12,365 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-23 17:04:22,115 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-23 17:04:22,847 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-25 12:46:23,031 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-25 12:46:29,129 - WARNING - django.request - Not Found: /favicon.ico
2025-08-25 12:46:29,151 - WARNING - django.request - Not Found: /favicon.ico
2025-08-25 12:46:29,162 - WARNING - django.request - Not Found: /favicon.ico
2025-08-25 12:46:29,514 - WARNING - django.request - Unauthorized: /api/user_info/
2025-08-25 12:46:29,520 - WARNING - django.request - Unauthorized: /api/user_info/
2025-08-28 18:25:45,997 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-28 18:25:52,613 - WARNING - django.request - Not Found: /favicon.ico
2025-08-28 18:25:52,630 - WARNING - django.request - Not Found: /favicon.ico
2025-08-28 18:25:52,639 - WARNING - django.request - Not Found: /favicon.ico
2025-08-28 18:25:52,944 - WARNING - django.request - Unauthorized: /api/user_info/
2025-08-28 18:25:52,947 - WARNING - django.request - Unauthorized: /api/user_info/
2025-08-28 18:33:29,703 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-28 18:33:31,043 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-28 18:34:00,119 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-28 18:34:01,632 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-28 18:37:09,143 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-28 18:37:10,653 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-28 18:42:27,533 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-28 18:42:38,820 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-28 18:48:38,824 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-28 18:48:40,009 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-28 18:49:08,027 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-28 18:49:09,102 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-28 18:49:30,250 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-28 18:49:31,239 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-28 18:50:40,277 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-28 18:50:41,292 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-28 18:57:34,697 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-28 18:57:45,812 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-30 10:46:57,032 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-30 10:47:03,477 - WARNING - django.request - Not Found: /favicon.ico
2025-08-30 10:47:03,775 - WARNING - django.request - Unauthorized: /api/user_info/
2025-08-30 10:47:03,777 - WARNING - django.request - Unauthorized: /api/user_info/
2025-08-30 10:47:45,884 - WARNING - django.request - Not Found: /api/files/03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf/
2025-08-30 10:47:45,942 - WARNING - django.request - Not Found: /favicon.ico
2025-08-30 11:04:37,085 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-30 11:04:37,886 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-30 11:04:47,662 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-30 11:04:48,262 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-30 11:05:06,844 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-30 11:05:07,441 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-30 11:05:21,218 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-30 11:05:21,858 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-30 11:06:44,710 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-30 11:06:55,513 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-30 11:08:05,937 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-30 11:08:06,778 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-30 11:09:14,151 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-30 11:09:14,724 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-30 11:09:27,470 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-30 11:09:28,016 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-30 11:09:56,908 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
2025-08-30 11:12:18,112 - INFO - django.utils.autoreload - D:\AI-Agent-Chatbot-main\chatbot\views.py changed, reloading.
2025-08-30 11:12:18,682 - INFO - django.utils.autoreload - Watching for file changes with StatReloader
