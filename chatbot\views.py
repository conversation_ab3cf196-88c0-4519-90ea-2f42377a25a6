from django.http import JsonResponse, HttpResponse, Http404
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.models import User
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from urllib.parse import unquote
from datetime import datetime
import weaviate
#from openai import OpenAI
from .models import PdfFile
from .upload import upload_pdf_to_db
import logging


# chatbot/views.py

import json
import os
from django.http import JsonResponse, HttpResponseForbidden, HttpResponseBadRequest
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import user_passes_test
from django.utils.decorators import method_decorator
from django.views import View



# views.py
# chatbot/views.py
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .models import PromptTemplate
from .serializers import PromptTemplateSerializer



import json
import os
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime
from .models import SupportTicket
from scipy.spatial.distance import cosine
from django.utils import timezone
from datetime import timedelta
from .models import SupportTicket  # Make sure this import exists
import openai
from .openai_usage_tracker import track_openai_chat_completion




AUTO_CLOSE_DAYS = 7  # days after which tickets auto-close

def auto_close_expired_tickets():
    """Close tickets older than AUTO_CLOSE_DAYS (default: 7 days)."""
    cutoff = timezone.now() - timedelta(days=AUTO_CLOSE_DAYS)
    expired = SupportTicket.objects.filter(status="open", created_at__lt=cutoff)
    count = expired.update(status="closed")
    if count:
        print(f"✅ Auto-closed {count} expired ticket(s).")

# --- pending tickets -------------------------------------------------

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def open_tickets(request):
    """
    Return all OPEN tickets for the logged-in user,
    each with a one-line 'summary' (added in serializer earlier).
    """
    auto_close_expired_tickets()          # ⏱ keep DB tidy

    tickets = SupportTicket.objects.filter(
        user=request.user,
        status="open"
    ).order_by('-last_activity')          # newest first

    serializer = SupportTicketSerializer(tickets, many=True)
    return Response(serializer.data)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def ticket_detail(request, ticket_number):
    """
    Fetch one ticket (only if it belongs to this user).
    Front-end uses this when the user clicks a ticket to resume it.
    """
    auto_close_expired_tickets()

    ticket = get_object_or_404(
        SupportTicket,
        user=request.user,
        ticket_number=ticket_number
    )
    ser = SupportTicketSerializer(ticket)
    return Response(ser.data)


# --- Pending-ticket list ---------------------------------------
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .models import SupportTicket

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def pending_tickets(request):
    tickets = []
    user = request.user

    open_tickets = SupportTicket.objects.filter(user=user, status="open").order_by('-last_activity')
    for t in open_tickets:
        desc_lines = (t.problem_description or "").splitlines()
        issue = desc_lines[0][:60] if desc_lines else "No description"

        tickets.append({
            "ticket_number": t.ticket_number,
            "status": t.status,
            "issue": issue,
            "title": t.short_title or f"{t.product_type} - {t.model_number}" if t.product_type and t.model_number else "No title",
            "short_title": t.short_title or "No title",
            "problem_description": t.problem_description or "No description available",
            "product_type": t.product_type,
            "model": t.model_number,
            "created_at": t.created_at,
            "last_activity": t.last_activity,
        })

    return Response({"tickets": tickets})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def closed_tickets(request):
    """
    Return all CLOSED tickets for the logged-in user.
    """
    tickets = []
    user = request.user

    closed_tickets_qs = SupportTicket.objects.filter(user=user, status="closed").order_by('-last_activity')
    for t in closed_tickets_qs:
        desc_lines = (t.problem_description or "").splitlines()
        issue = desc_lines[0][:60] if desc_lines else "No description"

        tickets.append({
            "ticket_number": t.ticket_number,
            "status": t.status,
            "issue": issue,
            "title": t.short_title or f"{t.product_type} - {t.model_number}" if t.product_type and t.model_number else "No title",
            "short_title": t.short_title or "No title",
            "problem_description": t.problem_description or "No description available",
            "solution_summary": t.solution_summary or "No solution summary available",
            "product_type": t.product_type,
            "model": t.model_number,
            "created_at": t.created_at,
            "last_activity": t.last_activity,
        })

    return Response({"tickets": tickets})




from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from .models import SupportTicket
from .serializers import SupportTicketSerializer

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def ticket_summary(request):
    """
    Return a summary of the ticket's problem, solution, and status.
    """
    ticket_number = request.data.get("ticket_number")
    if not ticket_number:
        return Response({"error": "Ticket number is required."}, status=status.HTTP_400_BAD_REQUEST)

    try:
        ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
    except SupportTicket.DoesNotExist:
        return Response({"error": "Ticket not found."}, status=status.HTTP_404_NOT_FOUND)

    serializer = SupportTicketSerializer(ticket)
    return Response(serializer.data)

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils.timezone import now
from datetime import timedelta
from chatbot.models import SupportTicket

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def pending_ticket_summaries(request):
    """
    Return list of user's open tickets created within last 7 days,
    each with ticket_number and short_title.
    """
    seven_days_ago = now() - timedelta(days=7)

    tickets = (
        SupportTicket.objects
        .filter(user=request.user, status="open", created_at__gte=seven_days_ago)
        .order_by('-created_at')
        .values('ticket_number', 'short_title')
    )

    ticket_list = [
        {
            "ticket_number": t['ticket_number'],
            "short_title": t['short_title'] if t['short_title'] else "No title"
        }
        for t in tickets
    ]

    if not ticket_list:
        return Response({"message": "No pending tickets found."})

    return Response(ticket_list)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def start_ticket_session(request, ticket_number):
    """
    Start a chatbot session with a specific ticket.
    Returns ticket details and initial message for the chatbot.
    """
    try:
        ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
    except SupportTicket.DoesNotExist:
        return Response({"error": "Ticket not found."}, status=404)

    if ticket.status == "closed":
        return Response({"error": "Cannot start session with a closed ticket."}, status=400)

    serializer = SupportTicketSerializer(ticket)

    # Prepare initial message based on ticket state
    if ticket.problem_description:
        # Get user's name
        username = ticket.user.name if hasattr(ticket.user, 'name') and ticket.user.name else ticket.user.official_email.split('@')[0]
        categories = ", ".join(ticket.problem_categories) if ticket.problem_categories else "general issues"
        initial_message = f'Welcome "{username}". Your ticket "{ticket.ticket_number}" has been raised. Please explain the problem related to the "{categories}".'
        awaiting_description = False
    else:
        # Get user's name for new tickets
        username = ticket.user.name if hasattr(ticket.user, 'name') and ticket.user.name else ticket.user.official_email.split('@')[0]
        categories = ", ".join(ticket.problem_categories) if ticket.problem_categories else "general issues"
        initial_message = f'Welcome "{username}". Your ticket "{ticket.ticket_number}" has been raised. Please explain the problem related to the "{categories}".'
        awaiting_description = True

    return Response({
        "ticket": serializer.data,
        "initial_message": initial_message,
        "session_type": "ticket",
        "awaiting_description": awaiting_description
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def start_general_session(request):
    """
    Start a general chatbot session without ticket context.
    """
    return Response({
        "initial_message": "Hello! I'm here to help with general questions about our products and services. How can I assist you today?",
        "session_type": "general",
        "ticket": None
    })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def end_session(request):
    """
    End the current session and optionally close ticket.
    This can be used for cleanup when user logs out or session ends.
    """
    ticket_number = request.data.get("ticket_number")
    close_ticket = request.data.get("close_ticket", False)

    if ticket_number and close_ticket:
        try:
            ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
            if ticket.status == "open":
                ticket.status = "closed"
                ticket.save(update_fields=["status"])
                return Response({"message": f"Session ended and ticket {ticket_number} closed."})
        except SupportTicket.DoesNotExist:
            pass

    return Response({"message": "Session ended successfully."})


PROMPTS_JSON_PATH = r"D:\AI-Agent-Chatbot-main\chatbot\prompt.json"

class PromptTemplateView(APIView):
    permission_classes = [AllowAny]  # Allow unauthenticated access for prompt templates

    def get(self, request):
        prompt_type = request.query_params.get("type")
        if not prompt_type:
            return Response({"error": "Prompt type is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            with open(PROMPTS_JSON_PATH, "r") as f:
                prompts = json.load(f)
        except FileNotFoundError:
            return Response({"error": "Prompts file not found"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        except json.JSONDecodeError:
            return Response({"error": "Prompts file is invalid JSON"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        prompt = prompts.get(prompt_type)
        if not prompt or not prompt.get("is_active"):
            return Response({"error": "No active prompt found"}, status=status.HTTP_404_NOT_FOUND)

        return Response({
            "name": prompt_type,
            "template": prompt.get("template"),
            "is_active": prompt.get("is_active"),
            "last_modified": prompt.get("last_modified"),
        })

    def post(self, request):
        data = request.data
        prompt_type = data.get("prompt_type")
        template = data.get("template")
        is_active = data.get("is_active", True)

        if not prompt_type or not template:
            return Response({"error": "prompt_type and template are required"}, status=status.HTTP_400_BAD_REQUEST)

        # Read current prompts from JSON
        try:
            with open(PROMPTS_JSON_PATH, "r") as f:
                prompts = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            prompts = {}

        # Deactivate all other prompts of the same type
        for key in prompts:
            if key == prompt_type:
                prompts[key]["is_active"] = False

        # Add/update the prompt, mark active per request
        prompts[prompt_type] = {
            "template": template,
            "is_active": is_active,
            "last_modified": datetime.utcnow().isoformat(),
        }

        # Save back to JSON file
        try:
            with open(PROMPTS_JSON_PATH, "w") as f:
                json.dump(prompts, f, indent=2)
        except Exception as e:
            return Response({"error": f"Failed to save prompts: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response({
            "name": prompt_type,
            "template": template,
            "is_active": is_active,
            "last_modified": prompts[prompt_type]["last_modified"],
        }, status=status.HTTP_201_CREATED)




@api_view(['POST'])
@permission_classes([IsAuthenticated])
def verify_organization(request):
    user = request.user
    input_org = request.data.get("organization", "").strip().lower()
    actual_org = user.organization.strip().lower() if user.organization else ""

    if input_org == actual_org:
        return Response({"status": "verified", "message": "✅ Organization matched. You are verified."})
    else:
        return Response({"status": "not_verified", "message": "❌ Organization mismatch. Please login with the correct account."}, status=400)



# views.py
# chatbot/views.py

def generate_gpt_summary(text, prompt_prefix, max_tokens=300):
    """
    Uses OpenAI GPT model to generate a concise summary of the given text.

    Args:
        text (str): The input text to summarize.
        prompt_prefix (str): Instruction prompt to guide summarization.
        max_tokens (int): Max tokens for the GPT response.

    Returns:
        str or None: The generated summary text or None on failure.
    """
    prompt = f"{prompt_prefix}\n\n{text}"
    try:
        response = track_openai_chat_completion(
            model=GPT_MODEL,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.5,
            max_tokens=max_tokens,
            purpose="gpt_summary"
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"GPT summary error: {e}")
        return None

def generate_ticket_content(product_info):
    """
    Generate Problem Description and Short Title using GPT-4.0 mini based on product information.

    Args:
        product_info (dict): Dictionary containing product details

    Returns:
        dict: Contains 'problem_description' and 'short_title' or None on failure
    """
    try:
        # Create a comprehensive prompt for generating ticket content
        prompt = f"""
Based on the following product information, generate a professional problem description and short title for a support ticket:

Product Type: {product_info.get('product_type', 'N/A')}
Brand: {product_info.get('brand', 'N/A')}
Model Number: {product_info.get('model_number', 'N/A')}
Serial Number: {product_info.get('serial_number', 'N/A')}
Operating System: {product_info.get('operating_system_detailed', 'N/A')}
Purchased From: {product_info.get('purchased_from', 'N/A')}
Year of Purchase: {product_info.get('year_of_purchase', 'N/A')}
PO Number: {product_info.get('po_number', 'N/A')}

Please generate:
1. A short title (max 60 characters) that summarizes the potential support need
2. A problem description (2-3 sentences) that describes common issues or setup requirements for this product

Format your response as:
SHORT_TITLE: [title here]
PROBLEM_DESCRIPTION: [description here]
"""

        response = track_openai_chat_completion(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.5,
            max_tokens=500,
            purpose="ticket_creation"
        )

        content = response.choices[0].message.content.strip()

        # Parse the response
        lines = content.split('\n')
        short_title = ""
        problem_description = ""

        for line in lines:
            if line.startswith("SHORT_TITLE:"):
                short_title = line.replace("SHORT_TITLE:", "").strip()
            elif line.startswith("PROBLEM_DESCRIPTION:"):
                problem_description = line.replace("PROBLEM_DESCRIPTION:", "").strip()

        # Fallback if parsing fails
        if not short_title:
            short_title = f"{product_info.get('product_type', 'Product')} - {product_info.get('model', 'Support')}"

        if not problem_description:
            problem_description = f"Support request for {product_info.get('product_type', 'product')} {product_info.get('model', 'model')}. Customer needs assistance with setup, configuration, or troubleshooting."

        return {
            'short_title': short_title[:120],  # Ensure it fits the field limit
            'problem_description': problem_description
        }

    except Exception as e:
        print(f"GPT ticket content generation error: {e}")
        # Return fallback content
        return {
            'short_title': f"{product_info.get('brand', 'Product')} {product_info.get('product_type', 'Support')} - {product_info.get('model_number', 'Support')}",
            'problem_description': f"Support request for {product_info.get('brand', 'product')} {product_info.get('product_type', 'product')} {product_info.get('model_number', 'model')}. Customer needs assistance with setup, configuration, or troubleshooting."
        }
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_info(request):
    user = request.user
    print("Logged-in user:", request.user)
    print("Name:", user.name)

    return Response({
        "name": user.name,  # ✅ This should match your DB column
        "email": user.official_email,
        "organization": user.organization
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_product_hierarchy(request):
    """
    Return the complete product hierarchy for hierarchical selection.
    """
    import os

    hierarchy_file_path = os.path.join(os.path.dirname(__file__), 'product_hierarchy.json')

    try:
        with open(hierarchy_file_path, 'r', encoding='utf-8') as f:
            hierarchy_data = json.load(f)

        return Response({
            "status": "success",
            "hierarchy": hierarchy_data
        })
    except FileNotFoundError:
        return Response({
            "status": "error",
            "message": "Product hierarchy file not found"
        }, status=404)
    except json.JSONDecodeError:
        return Response({
            "status": "error",
            "message": "Invalid product hierarchy data"
        }, status=500)
    except Exception as e:
        return Response({
            "status": "error",
            "message": f"Error loading product hierarchy: {str(e)}"
        }, status=500)

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .serializers import SupportTicketSerializer

 # import your GPT helper function here

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_ticket(request):
    # Extract product information from request
    # Map 'software' field to 'sdk_software_used' for backward compatibility
    software_value = request.data.get('software', '') or request.data.get('sdk_software_used', '')

    # Parse software field to extract name and version
    # Format: "Sapera LT (v9.0)" or "Spinnaker (v4.2)"
    sdk_software_name = ''
    sdk_version_value = request.data.get('sdk_version', '')

    if software_value:
        if '(' in software_value and ')' in software_value:
            # Extract software name and version from combined field
            parts = software_value.split('(')
            sdk_software_name = parts[0].strip()
            version_part = parts[1].replace(')', '').strip()
            if version_part and not sdk_version_value:
                sdk_version_value = version_part
        else:
            sdk_software_name = software_value

    product_info = {
        'product_type': request.data.get('product_type', ''),
        'brand': request.data.get('brand', ''),
        'sensor_type': request.data.get('sensor_type', ''),
        'family_name': request.data.get('family_name', ''),
        'model_number': request.data.get('model_number', ''),
        'serial_number': request.data.get('serial_number', ''),
        'sdk_software_used': sdk_software_name,
        'sdk_version': sdk_version_value,
        'programming_language': request.data.get('programming_language', ''),
        'camera_configuration_tool': request.data.get('camera_configuration_tool', ''),
        'operating_system_detailed': request.data.get('operating_system_detailed', ''),
        'purchased_from': request.data.get('purchased_from', ''),
        'year_of_purchase': request.data.get('year_of_purchase', ''),
        'po_number': request.data.get('po_number', ''),
        # Hierarchical product selection fields
        'product_hierarchy_path': request.data.get('product_hierarchy_path', []),
        'product_category': request.data.get('product_category', ''),
        'product_subcategory': request.data.get('product_subcategory', ''),
        'product_family': request.data.get('product_family', ''),
        'product_interface': request.data.get('product_interface', ''),
    }

    # Generate Problem Description and Short Title using GPT-4.0 mini
    generated_content = generate_ticket_content(product_info)

    # Add generated content to the request data
    ticket_data = request.data.copy()
    ticket_data['problem_description'] = generated_content['problem_description']
    ticket_data['short_title'] = generated_content['short_title']

    # Ensure hierarchical fields are included
    ticket_data['product_hierarchy_path'] = product_info['product_hierarchy_path']
    ticket_data['product_category'] = product_info['product_category']
    ticket_data['product_subcategory'] = product_info['product_subcategory']
    ticket_data['product_family'] = product_info['product_family']
    ticket_data['product_interface'] = product_info['product_interface']

    # Ensure SDK software fields are properly mapped
    ticket_data['sdk_software_used'] = product_info['sdk_software_used']
    ticket_data['sdk_version'] = product_info['sdk_version']

    serializer = SupportTicketSerializer(data=ticket_data)

    if serializer.is_valid():
        # Save ticket with AI-generated content
        ticket = serializer.save(user=request.user)

        # Generate problem summary from the AI-generated problem description
        problem_summary = generate_gpt_summary(
            ticket.problem_description,
            "Summarize this problem description clearly and professionally:"
        )
        if problem_summary:
            ticket.problem_summary = problem_summary
            ticket.save(update_fields=["problem_summary"])

        return Response({
            "status": "success",
            "message": "Ticket created successfully.",
            "ticket_number": ticket.ticket_number,
            "generated_content": {
                "short_title": ticket.short_title,
                "problem_description": ticket.problem_description
            }
        })
    else:
        return Response({"status": "error", "errors": serializer.errors}, status=400)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_ticket_categories(request):
    """
    Update problem categories for a ticket.
    """
    ticket_number = request.data.get('ticket_number')
    problem_categories = request.data.get('problem_categories', [])

    if not ticket_number:
        return Response({"status": "error", "message": "Ticket number is required."}, status=400)

    try:
        ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
    except SupportTicket.DoesNotExist:
        return Response({"status": "error", "message": "Ticket not found."}, status=404)

    # Update problem categories
    ticket.problem_categories = problem_categories
    ticket.save(update_fields=['problem_categories'])

    return Response({
        "status": "success",
        "message": "Problem categories updated successfully.",
        "ticket_number": ticket.ticket_number,
        "problem_categories": ticket.problem_categories
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_ticket_description(request):
    """
    Update problem description for a ticket.
    """
    ticket_number = request.data.get('ticket_number')
    problem_description = request.data.get('problem_description', '')

    if not ticket_number:
        return Response({"status": "error", "message": "Ticket number is required."}, status=400)

    if not problem_description.strip():
        return Response({"status": "error", "message": "Problem description is required."}, status=400)

    try:
        ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
    except SupportTicket.DoesNotExist:
        return Response({"status": "error", "message": "Ticket not found."}, status=404)

    # Update problem description
    ticket.problem_description = problem_description.strip()

    # Generate problem summary from the problem description
    if ticket.problem_description:
        problem_summary = generate_gpt_summary(
            ticket.problem_description,
            "Summarize this problem description clearly and professionally:"
        )
        if problem_summary:
            ticket.problem_summary = problem_summary

    ticket.save(update_fields=['problem_description', 'problem_summary'])

    # Generate initial prompt with document context
    try:
        # Get relevant document context for the problem description
        matches = search_similar_chunks_weaviate(problem_description, limit=5)
        context_chunks = [m["content"] for m in matches] if matches else []

        # Generate and save the initial prompt
        get_or_generate_ticket_prompt(
            ticket=ticket,
            user_description=problem_description,
            context_chunks=context_chunks,
            force_regenerate=True
        )

        print(f"✅ Initial prompt generated for ticket {ticket.ticket_number}")
    except Exception as e:
        print(f"❌ Error generating initial prompt: {e}")
        # Don't fail the request if prompt generation fails

    return Response({
        "status": "success",
        "message": "Problem description updated successfully.",
        "ticket_number": ticket.ticket_number,
        "problem_description": ticket.problem_description,
        "problem_summary": ticket.problem_summary
    })



from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from chatbot.models import SupportTicket

# Your API key here or from environment/config
openai.api_key = "********************************************************************************************************************************************************************"

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from openai import OpenAIError  # Import specific OpenAI error

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from openai import OpenAIError
from collections import Counter

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_problem_description(request):
    ticket_number = request.data.get("ticket_number")
    raw_description = (request.data.get("problem_description") or "").strip()

    if not ticket_number or not raw_description:
        return Response({"error": "ticket_number and problem_description are required"}, status=400)

    # Fetch ticket that belongs to requesting user
    ticket = get_object_or_404(SupportTicket, ticket_number=ticket_number, user=request.user)

    # Check if ticket is in a valid state
    if ticket.status == "closed":
        return Response({"error": "Cannot add problem description to a closed ticket"}, status=400)

    # 1️⃣ Rewrite description
    try:
        rewrite_resp = track_openai_chat_completion(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "Rewrite user complaints professionally."},
                {"role": "user", "content": raw_description},
            ],
            user=request.user,
            purpose="problem_description_rewrite"
        )
        cleaned_description = rewrite_resp.choices[0].message.content.strip()
    except OpenAIError as e:
        print(f"❌ OpenAI error during description rewrite: {e}")
        return Response({"error": f"Failed to rewrite description: {e}"}, status=500)
    except Exception as e:
        print(f"❌ Unexpected error during description rewrite: {e}")
        return Response({"error": f"Unexpected error: {e}"}, status=500)

    # Store refined problem text
    ticket.problem_description = cleaned_description
    ticket.save(update_fields=["problem_description"])

    # 2️⃣ Generate & save problem summary
    try:
        problem_summary = generate_gpt_summary(
            cleaned_description,
            "Summarize the following user problem in 2 lines:"
        ) or "No summary yet."
        ticket.problem_summary = problem_summary.strip()
        ticket.save(update_fields=["problem_summary"])
        print(f"✅ Problem summary saved for ticket {ticket.ticket_number}")
    except OpenAIError as e:
        print(f"❌ OpenAI error during problem summary generation: {e}")
    except Exception as e:
        print(f"❌ Unexpected error during problem summary generation: {e}")

    # 3️⃣ Build query with ticket metadata for accurate retrieval
    db_ctx = {
        "productType": ticket.product_type,
        "purchasedFrom": ticket.purchased_from,
        "yearOfPurchase": ticket.year_of_purchase,
        "brand": ticket.brand,
        "modelNumber": ticket.model_number,
        "serialNumber": ticket.serial_number,
        "operatingSystem": ticket.operating_system_detailed,
    }
    full_query = build_full_query(
        user_query=cleaned_description,
        product_ctx=db_ctx,
        problem_description=cleaned_description,
        solution_summary=None
    )

# Run initial query with high certainty
    result = retrieve_and_generate_answer(full_query, user_id=request.user.id, top_k=5)

    if not result or not result.get("answer") or result.get("answer") == "No answer.":
        # Retry with lower certainty
        print("🔁 No strong result. Retrying with lower certainty...")
        result = retrieve_and_generate_answer(full_query, user_id=request.user.id, top_k=5, certainty_threshold=0.5)

    if not result or not result.get("answer") or result.get("answer") == "No answer.":
        # Final fallback message
        answer_text = "I couldn't find any relevant support documents for your query. Please try rephrasing your question or contact support directly."
        file_scores = {}
        primary_reference_file = None
    else:
        answer_text = result.get("answer", "No answer.")
        file_scores = result.get("file_scores", {})
        primary_reference_file = result.get("primary_reference_file")


    # 📎 Download Offer Logic: Create file objects based on primary reference file
    # Map source_file names to actual database entries to get correct download links
    file_objs = []
    if primary_reference_file:
        # Handle both single file and list of files
        source_files_to_process = primary_reference_file if isinstance(primary_reference_file, list) else [primary_reference_file]

        # Map each source file to database entry
        for source_filename in source_files_to_process:
            try:
                # Look up the file in the database by filename
                pdf_file = PdfFile.objects.get(file_name=source_filename)
                file_objs.append({
                    "filename": source_filename,
                    "url": f"/api/download-pdf/{pdf_file.id}/",  # Use new ID-based endpoint
                    "pdf_id": pdf_file.id,
                    "score": file_scores.get(source_filename, 0.0)
                })
                print(f"🔍 DEBUG: Mapped {source_filename} to database ID {pdf_file.id} -> /api/download-pdf/{pdf_file.id}/")
            except PdfFile.DoesNotExist:
                print(f"⚠️ WARNING: Source file '{source_filename}' not found in database - skipping download offer")
                continue

        # Sort by score (highest first) to show most relevant files first
        file_objs.sort(key=lambda x: x['score'], reverse=True)






    # 5️⃣ Generate & save a two-line summary of the answer
    try:
        summary = generate_gpt_summary(
            answer_text,
            "Summarize the following solution in 2 lines:"
        ) or "No solution yet."
        ticket.solution_summary = summary.strip()
        ticket.save(update_fields=["solution_summary"])
        print(f"✅ Solution summary saved for ticket {ticket.ticket_number}")
    except OpenAIError as e:
        print(f"❌ OpenAI error during solution summary generation: {e}")
    except Exception as e:
        print(f"❌ Unexpected error during solution summary generation: {e}")

    # 6️⃣ Return to front-end
    return Response({
        "status": "success",
        "answer": answer_text,
        "files": file_objs,
    })



from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .models import SupportTicket


from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from chatbot.models import SupportTicket  # Adjust import path as needed

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_ticket_status(request):
    ticket_number = request.data.get("ticket_number")
    status = request.data.get("status")  # Expected: 'open' or 'closed'

    if status not in ["open", "closed"]:
        return Response({"error": "Invalid status."}, status=400)

    if not ticket_number:
        return Response({"error": "Ticket number is required."}, status=400)

    try:
        # Find ticket by ticket_number and user
        ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
    except SupportTicket.DoesNotExist:
        return Response({"error": "Ticket not found."}, status=404)

    ticket.status = status
    ticket.save()
    return Response({"success": True, "message": f"Ticket {ticket_number} marked as {status}."})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def view_ticket_prompt(request, ticket_number):
    """
    View the generated prompt for a specific ticket (for debugging/admin purposes).
    """
    try:
        ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
    except SupportTicket.DoesNotExist:
        return Response({"error": "Ticket not found."}, status=404)

    if not ticket.generated_prompt:
        return Response({"error": "No prompt generated for this ticket yet."}, status=404)

    return Response({
        "ticket_number": ticket.ticket_number,
        "generated_prompt": ticket.generated_prompt,
        "prompt_file_path": ticket.prompt_file_path,
        "last_updated": ticket.last_activity
    })


# === Configuration ===
WEAVIATE_CLASS_NAME = "ChunkEmbeddingsV2"  # Legacy class
AREA_SCAN_CLASS_NAME = "AreaScanChunks"
LINE_SCAN_CLASS_NAME = "LineScanChunks"
#OPENAI_API_KEY = "********************************************************************************************************************************************************************"
EMBEDDING_MODEL = "text-embedding-ada-002"
GPT_MODEL = "gpt-4o-mini"

WEAVIATE_URL = "http://localhost:8080"
CACHE_CLASS_NAME = "CachedQuestions"
EMBEDDING_MODEL = "text-embedding-ada-002"
GPT_MODEL = "gpt-4o-mini"

client = weaviate.Client(WEAVIATE_URL)
#openai_client = OpenAI(api_key="********************************************************************************************************************************************************************")  # Replace with your actual key

#def get_embedding(text):
    #response = openai_client.embeddings.create(input=text, model=EMBEDDING_MODEL)
   # return response.data[0].embedding
def get_embedding(text):
    response = openai.Embedding.create(input=text, model=EMBEDDING_MODEL)
    return response.data[0].embedding


def search_cache(query_vector, certainty_threshold=0.9):
    try:
        near_vector = {"vector": query_vector, "certainty": certainty_threshold}
        result = client.query.get(CACHE_CLASS_NAME, ["text", "answer", "source_files"]) \
                        .with_near_vector(near_vector) \
                        .with_limit(1) \
                        .do()

        matches = result.get("data", {}).get("Get", {}).get(CACHE_CLASS_NAME, [])
        if matches:
            match = matches[0]
            return {
                "query_text": match["text"],
                "answer": match["answer"],
                "files": match.get("source_files", []),
            }
        return None

    except Exception as e:
        print("❌ Weaviate cache search error:", e)
        return None


# ───────────────────────────────────────────────────────────────────
# OLD:
# def add_to_cache(query_text, answer_text, vector):

def add_to_cache(query_text, answer_text, vector, source_files):
    """
    Store a Q/A plus its vector & related source files list.
    """
    try:
        client.data_object.create(
            data_object={
                "text": query_text,
                "answer": answer_text,
                "source_files": source_files,  # matches schema property name
            },
            class_name=CACHE_CLASS_NAME,
            vector=vector
        )
    except Exception as e:
        print("❌ Weaviate cache insert error:", e)





#client_openai = OpenAI(api_key="********************************************************************************************************************************************************************")
chat_history = []
from django.http import HttpResponse, Http404
from .models import PdfFile  # Replace with your actual model name

from urllib.parse import unquote

@api_view(['GET'])
def serve_file_view(request, filename):
    decoded_filename = unquote(filename)  # Decode %20, %28, %29 etc
    try:
        file_entry = PdfFile.objects.get(file_name=decoded_filename)
        response = HttpResponse(file_entry.file_data, content_type='application/pdf')
        response['Content-Disposition'] = f'inline; filename="{decoded_filename}"'
        return response
    except PdfFile.DoesNotExist:
        raise Http404(f"No such file: {decoded_filename}")



# === In-memory per-user chat history (not persistent) ===
chat_history_per_user = {}

def get_user_history(user_id):
    return chat_history_per_user.get(user_id, [])

def add_to_user_history(user_id, query, answer):
    history = chat_history_per_user.get(user_id, [])
    history.append((query, answer))
    if len(history) > 20:
        history = history[-20:]
    chat_history_per_user[user_id] = history

# === Signup View ===

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import CustomUser, ChatHistory
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.models import User
import json

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.views import TokenObtainPairView
from .serializers import EmailTokenObtainPairSerializer
from rest_framework.permissions import IsAuthenticated
User = get_user_model()


@api_view(['POST'])
@permission_classes([AllowAny])
def signup_view(request):
    data = request.data

    # Explicit required fields validation
    required_fields = ['state', 'name', 'address', 'organization', 'official_email', 'phone', 'mobile', 'password', 'password2']

    for field in required_fields:
        if not data.get(field):
            return Response({"error": f"Missing required field: {field}"}, status=status.HTTP_400_BAD_REQUEST)

    # Password match check
    if data['password'] != data['password2']:
        return Response({"error": "Passwords do not match"}, status=status.HTTP_400_BAD_REQUEST)

    # Check if user exists
    if User.objects.filter(official_email=data['official_email']).exists():
        return Response({"error": "User with this email already exists"}, status=status.HTTP_400_BAD_REQUEST)

    # Create user with provided data
    user = User.objects.create_user(
        official_email=data['official_email'],
        password=data['password'],
        state=data['state'],
        name=data['name'],
        address=data['address'],
        organization=data['organization'],
        alt_email=data.get('alt_email', ''),  # optional field
        phone=data['phone'],
        mobile=data['mobile'],
    )

    return Response({"message": "User created successfully"}, status=status.HTTP_201_CREATED)

class EmailLoginView(TokenObtainPairView):
    serializer_class = EmailTokenObtainPairSerializer


from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from datetime import datetime
from .upload import upload_pdf_to_db  # Your upload logic

from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse

@csrf_exempt
def upload_pdf_view(request):
    if request.method != 'POST':
        return JsonResponse({'error': 'Only POST method allowed'}, status=405)

    try:
        files = request.FILES.getlist('pdf_file')
        camera_type = request.POST.get('camera_type')

        if not files:
            return JsonResponse({'error': 'No files received'}, status=400)

        if not camera_type:
            return JsonResponse({'error': 'Camera type is required'}, status=400)

        if camera_type not in ['area_scan', 'line_scan']:
            return JsonResponse({'error': 'Invalid camera type. Must be area_scan or line_scan'}, status=400)

        success_count = 0
        for file in files:
            binary_data = file.read()
            filename = file.name
            uploaded = upload_pdf_to_db(filename, binary_data, datetime.now(), camera_type)
            if uploaded:
                success_count += 1

        return JsonResponse({'message': f'{success_count} file(s) uploaded successfully to {camera_type} category'}, status=200)
    except Exception as e:
        return JsonResponse({'error': f'Exception: {str(e)}'}, status=500)

    
    

from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import get_authorization_header
from rest_framework_simplejwt.tokens import UntypedToken
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from django.http import HttpResponse, Http404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from urllib.parse import unquote

from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework import exceptions

@api_view(['GET'])
@permission_classes([AllowAny])
def serve_file_view(request, filename):
    # Try to authenticate from ?token= query param
    token = request.query_params.get('token')
    if token:
        try:
            validated_token = JWTAuthentication().get_validated_token(token)
            user = JWTAuthentication().get_user(validated_token)
            request.user = user
        except exceptions.AuthenticationFailed:
            return Response({"detail": "Invalid token."}, status=401)
    else:
        # You can also check request.headers for 'Authorization' if you want
        # or reject unauthenticated requests
        return Response({"detail": "Authentication credentials were not provided."}, status=401)

    decoded_filename = unquote(filename)
    try:
        file_entry = PdfFile.objects.get(file_name=decoded_filename)
        response = HttpResponse(file_entry.file_data, content_type='application/pdf')
        response['Content-Disposition'] = f'inline; filename="{decoded_filename}"'
        return response
    except PdfFile.DoesNotExist:
        raise Http404(f"No such file: {decoded_filename}")


@api_view(['GET'])
@permission_classes([AllowAny])
def download_pdf_by_id(request, pdf_id):
    """
    Download PDF file by database ID instead of filename.
    This provides a more reliable download method that doesn't depend on filename encoding.
    """
    # Try to authenticate from ?token= query param
    token = request.query_params.get('token')
    if token:
        try:
            validated_token = JWTAuthentication().get_validated_token(token)
            user = JWTAuthentication().get_user(validated_token)
            request.user = user
        except exceptions.AuthenticationFailed:
            return Response({"detail": "Invalid token."}, status=401)
    else:
        return Response({"detail": "Authentication credentials were not provided."}, status=401)

    try:
        file_entry = PdfFile.objects.get(id=pdf_id)
        response = HttpResponse(file_entry.file_data, content_type='application/pdf')
        response['Content-Disposition'] = f'inline; filename="{file_entry.file_name}"'
        return response
    except PdfFile.DoesNotExist:
        raise Http404(f"No PDF file found with ID: {pdf_id}")




def get_prompt_from_json(prompt_type):
    PROMPTS_JSON_PATH = r"D:\AI-Agent-Chatbot-main\chatbot\prompt.json"
    try:
        with open(PROMPTS_JSON_PATH, "r") as f:
            prompts = json.load(f)
        prompt_data = prompts.get(prompt_type)
        if not prompt_data or not prompt_data.get("is_active"):
            raise ValueError(f"No active prompt found for type: {prompt_type}")
        return prompt_data["template"]
    except Exception as e:
        raise ValueError(f"Prompt loading error: {str(e)}")


# ═══════════════════════════════════════════════════════════════════════════════
# PROMPT GENERATION AND MANAGEMENT FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

def get_ticket_chat_history(ticket):
    """
    Retrieve chat history for a specific ticket from the database.
    Returns a list of tuples: [(user_message, bot_response), ...]
    """
    chat_messages = ChatHistory.objects.filter(ticket=ticket).order_by('created_at')
    return [(msg.user_message, msg.bot_response) for msg in chat_messages]


def save_chat_message(ticket, user_message, bot_response):
    """
    Save a chat exchange to the database for a specific ticket.
    """
    ChatHistory.objects.create(
        ticket=ticket,
        user_message=user_message,
        bot_response=bot_response
    )


def generate_final_prompt(ticket, user_description, context_chunks):
    """
    Generate the final prompt according to the specified format.

    Args:
        ticket: SupportTicket instance
        user_description: Current user's problem description
        context_chunks: List of relevant document context chunks

    Returns:
        str: The formatted prompt
    """
    # Get chat history for this ticket
    chat_history = get_ticket_chat_history(ticket)

    # Format product details with hierarchical information
    hierarchy_path = ' > '.join(ticket.product_hierarchy_path) if ticket.product_hierarchy_path else 'Not specified'
    product_details = f"""[PRODUCT DETAILS]
- Product Hierarchy: {hierarchy_path}
- Product Category: {ticket.product_category or 'Not specified'}
- Product Subcategory: {ticket.product_subcategory or 'Not specified'}
- Product Family: {ticket.product_family or 'Not specified'}
- Product Interface: {ticket.product_interface or 'Not specified'}
- Brand: {ticket.brand or 'Not specified'}
- Sensor Type: {ticket.sensor_type or 'Not specified'}
- Model: {ticket.model_number or 'Not specified'}
- Serial Number: {ticket.serial_number or 'Not specified'}
- SDK: {ticket.sdk_software_used or 'Not specified'} ({ticket.sdk_version or 'Not specified'})
- Programming Language: {ticket.programming_language or 'Not specified'}
- Configuration Tool: {ticket.camera_configuration_tool or 'Not specified'}
- Operating System: {ticket.operating_system_detailed or 'Not specified'}"""

    # Format issue categories
    categories = ', '.join(ticket.problem_categories) if ticket.problem_categories else 'Not specified'
    issue_category = f"""[ISSUE CATEGORY]
- Category: {categories}"""

    # Format user's description
    user_desc_section = f"""[USER'S DESCRIPTION]
"{user_description or ticket.problem_description or 'No description provided'}" """

    # Format chat history
    chat_history_section = "[CHAT HISTORY]\nFull previous conversation with the user:"
    if chat_history:
        for i, (user_msg, bot_reply) in enumerate(chat_history, 1):
            chat_history_section += f"\n{i}. User: \"{user_msg}\"\n   Bot: \"{bot_reply}\""
    else:
        chat_history_section += "\n(No previous conversation)"

    # Format document context
    document_context = "[DOCUMENT CONTEXT]\n(Extracted chunks from uploaded user document or vector DB):"
    if context_chunks:
        for i, chunk in enumerate(context_chunks, 1):
            document_context += f"\n{i}. \"{chunk}\""
    else:
        document_context += "\n(No relevant document context found)"

    # Combine all sections
    final_prompt = f"""{product_details}

{issue_category}

{user_desc_section}

{chat_history_section}

{document_context}"""

    return final_prompt


def save_prompt_to_files(ticket, prompt):
    """
    Save the generated prompt to both MySQL and external JSON file.

    Args:
        ticket: SupportTicket instance
        prompt: The generated prompt string
    """
    import os

    # Save to MySQL
    ticket.generated_prompt = prompt

    # Create support_prompts directory if it doesn't exist
    prompts_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'support_prompts')
    os.makedirs(prompts_dir, exist_ok=True)

    # Save to JSON file
    json_filename = f"{ticket.ticket_number}.json"
    json_filepath = os.path.join(prompts_dir, json_filename)

    prompt_data = {
        "ticket_number": ticket.ticket_number,
        "prompt": prompt,
        "last_updated": timezone.now().isoformat()
    }

    try:
        with open(json_filepath, 'w', encoding='utf-8') as f:
            json.dump(prompt_data, f, indent=2, ensure_ascii=False)

        # Update the file path in the ticket
        ticket.prompt_file_path = json_filepath
        ticket.save(update_fields=['generated_prompt', 'prompt_file_path'])

        print(f"✅ Prompt saved for ticket {ticket.ticket_number}")
        return True
    except Exception as e:
        print(f"❌ Error saving prompt to file: {e}")
        # Still save to MySQL even if file save fails
        ticket.save(update_fields=['generated_prompt'])
        return False


def get_or_generate_ticket_prompt(ticket, user_description=None, context_chunks=None, force_regenerate=False):
    """
    Get existing prompt from MySQL or generate a new one if needed.

    Args:
        ticket: SupportTicket instance
        user_description: Current user's problem description (for updates)
        context_chunks: List of relevant document context chunks
        force_regenerate: Force regeneration even if prompt exists

    Returns:
        str: The prompt to use for GPT
    """
    # Check if we need to regenerate
    should_regenerate = (
        force_regenerate or
        not ticket.generated_prompt or
        (user_description and user_description != ticket.problem_description)
    )

    if should_regenerate:
        print(f"🎯 DEBUG: 🔄 Generating NEW TICKET PROMPT for ticket {ticket.ticket_number}")

        # Update problem description if provided
        if user_description and user_description != ticket.problem_description:
            ticket.problem_description = user_description
            ticket.save(update_fields=['problem_description'])

        # Generate new prompt
        prompt = generate_final_prompt(ticket, user_description, context_chunks or [])

        # Save to both MySQL and JSON file
        save_prompt_to_files(ticket, prompt)

        print(f"🎯 DEBUG: ✅ NEW TICKET PROMPT generated and saved ({len(prompt)} chars)")
        return prompt
    else:
        print(f"🎯 DEBUG: 📋 Using EXISTING TICKET PROMPT for ticket {ticket.ticket_number}")
        print(f"🎯 DEBUG: ✅ EXISTING TICKET PROMPT retrieved ({len(ticket.generated_prompt)} chars)")
        return ticket.generated_prompt

# === OpenAI + Weaviate helper functions ===

def get_embedding(text):
    response = openai.Embedding.create(input=text, model=EMBEDDING_MODEL)
    return response.data[0].embedding

def generate_answer(query, context_chunks, history_tuples, ticket=None):
    """
    Generate answer using either ticket-specific prompt or general chat prompt.
    Enhanced with anti-hallucination measures.
    """
    print(f"🎯 DEBUG: generate_answer called with {len(context_chunks)} context chunks")

    # Check if we have any context chunks
    if not context_chunks or all(not chunk.strip() for chunk in context_chunks):
        print("🎯 DEBUG: No valid context chunks - returning fallback message")
        return "No relevant support documents found. Please try rephrasing your question or contact support directly."

    # Log context chunks for debugging
    print(f"🎯 DEBUG: Context chunks:")
    for i, chunk in enumerate(context_chunks):
        print(f"  {i+1}. '{chunk[:100]}...' (length: {len(chunk)})")

    if ticket:
        # Use ticket-specific prompt system
        print(f"🎯 DEBUG: Using TICKET PROMPT for ticket {ticket.ticket_number}")
        try:
            # Get or generate the ticket prompt
            filled_prompt = get_or_generate_ticket_prompt(
                ticket=ticket,
                user_description=query,
                context_chunks=context_chunks
            )

            # Add the current query and anti-hallucination instructions
            filled_prompt += f"\n\n[CURRENT QUERY]\n\"{query}\""
            filled_prompt += f"\n\n[INSTRUCTIONS]\nIMPORTANT: Only use information from the provided document context above. If the context doesn't contain relevant information to answer the query, respond with 'No relevant support documents found for this specific question.' Do not generate answers based on general knowledge. Always include the source file name when referencing information."

            print(f"🎯 DEBUG: Ticket prompt length: {len(filled_prompt)} characters")

        except Exception as e:
            print(f"❌ Error with ticket prompt: {e}")
            return f"❌ Ticket Prompt Error: {str(e)}"
    else:
        # Use general chat prompt system with anti-hallucination measures
        print(f"🎯 DEBUG: Using GENERAL CHAT PROMPT (no ticket)")
        context_text = "\n".join(context_chunks) if context_chunks else "No relevant context found."
        history_text = "\n".join(f"Q: {q}\nA: {a}" for q, a in history_tuples[-5:]) if history_tuples else "No previous conversation."

        # Create anti-hallucination prompt
        filled_prompt = f"""Based on the following support document context, answer the user's question.

DOCUMENT CONTEXT:
{context_text}

CHAT HISTORY:
{history_text}

USER QUESTION: {query}

INSTRUCTIONS: Only use information from the provided document context above. If the context doesn't contain relevant information to answer the query, respond with 'No relevant support documents found for this specific question.' Do not generate answers based on general knowledge. Always include the source file name when referencing information."""

        print(f"🎯 DEBUG: General prompt length: {len(filled_prompt)} characters")

    # Print the actual prompt being used for debugging
    print(f"🎯 DEBUG: ===== ACTUAL PROMPT BEING SENT TO GPT =====")
    print(filled_prompt[:500] + "..." if len(filled_prompt) > 500 else filled_prompt)
    print(f"🎯 DEBUG: ===== END OF PROMPT (total length: {len(filled_prompt)}) =====")

    try:
        completion = track_openai_chat_completion(
            model=GPT_MODEL,
            messages=[{"role": "user", "content": filled_prompt}],
            temperature=0.3,  # Lower temperature to reduce hallucination
            max_tokens=2000,
            purpose="chat_response"
        )
        answer = completion.choices[0].message.content.strip()

        print(f"🎯 DEBUG: GPT response length: {len(answer)} characters")
        print(f"🎯 DEBUG: GPT response preview: '{answer[:200]}...'")

        # Save chat history for ticket mode
        if ticket:
            save_chat_message(ticket, query, answer)

        return answer
    except Exception as e:
        print(f"❌ OpenAI error: {e}")
        return f"❌ OpenAI error: {e}"



def get_weaviate_class_for_camera_type(camera_type):
    """Get the appropriate Weaviate class name based on camera type."""
    if camera_type == "area_scan":
        return AREA_SCAN_CLASS_NAME
    elif camera_type == "line_scan":
        return LINE_SCAN_CLASS_NAME
    else:
        return WEAVIATE_CLASS_NAME  # Legacy class for backward compatibility

def extract_camera_type_from_context(product_context):
    """Extract camera type from product context."""
    if not product_context:
        return None

    # Check for sensor type which might indicate camera type
    sensor_type = product_context.get("sensorType", "").lower()
    if "area" in sensor_type:
        return "area_scan"
    elif "line" in sensor_type:
        return "line_scan"

    # Check for family name or model which might indicate camera type
    family_name = product_context.get("familyName", "").lower()
    model_number = product_context.get("modelNumber", "").lower()

    # Common area scan indicators
    area_scan_keywords = ["area", "matrix", "ccd", "cmos"]
    # Common line scan indicators
    line_scan_keywords = ["line", "linear", "tdi"]

    text_to_check = f"{family_name} {model_number}".lower()

    for keyword in area_scan_keywords:
        if keyword in text_to_check:
            return "area_scan"

    for keyword in line_scan_keywords:
        if keyword in text_to_check:
            return "line_scan"

    return None

def search_similar_chunks_weaviate(query, limit=5, camera_type=None, certainty_threshold=0.7):
    try:
        print(f"🔍 DEBUG: Starting search with query: '{query[:100]}...'")
        print(f"🔍 DEBUG: Certainty threshold: {certainty_threshold}, Limit: {limit}")

        query_embedding = get_embedding(query)
        print(f"🔍 DEBUG: Generated embedding with {len(query_embedding)} dimensions")

        client = weaviate.Client(url="http://localhost:8080")

        # Determine which class(es) to search
        if camera_type:
            class_name = get_weaviate_class_for_camera_type(camera_type)
            classes_to_search = [class_name]
        else:
            # Search all camera-specific classes if no specific type is provided
            classes_to_search = [AREA_SCAN_CLASS_NAME, LINE_SCAN_CLASS_NAME, WEAVIATE_CLASS_NAME]

        schema = client.schema.get()
        available_classes = [cls["class"] for cls in schema.get("classes", [])]
        print(f"🔍 DEBUG: Available Weaviate classes: {available_classes}")

        all_results = []

        for class_name in classes_to_search:
            if class_name not in available_classes:
                print(f"⚠️ Collection '{class_name}' not found in Weaviate")
                continue

            print(f"🔍 DEBUG: Searching in class '{class_name}'...")

            # First, let's check if the collection has any data
            try:
                count_response = client.query.aggregate(class_name).with_meta_count().do()
                count = count_response.get("data", {}).get("Aggregate", {}).get(class_name, [{}])[0].get("meta", {}).get("count", 0)
                print(f"🔍 DEBUG: Class '{class_name}' contains {count} objects")

                if count == 0:
                    print(f"⚠️ Class '{class_name}' is empty, skipping...")
                    continue

            except Exception as e:
                print(f"❌ Error checking count for class '{class_name}': {e}")

            try:
                response = (
                    client.query
                    .get(class_name, ["source_file", "chunk_number", "content", "camera_type"])
                    .with_near_vector({"vector": query_embedding, "certainty": certainty_threshold})
                    .with_additional(["certainty", "vector"])
                    .with_limit(limit)
                    .do()
                )

                results = response.get("data", {}).get("Get", {}).get(class_name, [])
                print(f"🔍 DEBUG: Found {len(results)} results in class '{class_name}'")

                # Log details of retrieved chunks
                for i, result in enumerate(results):
                    certainty = result.get("_additional", {}).get("certainty", 0.0)
                    source_file = result.get("source_file", "Unknown")
                    content_preview = result.get("content", "")[:100] + "..." if result.get("content") else "No content"
                    print(f"🔍 DEBUG: Result {i+1}: certainty={certainty:.3f}, source={source_file}, content='{content_preview}'")

                all_results.extend(results)
            except Exception as e:
                print(f"❌ Error searching in class '{class_name}': {e}")
                continue

        # Sort all results by certainty (highest first) and limit
        all_results.sort(key=lambda x: x.get("_additional", {}).get("certainty", 0.0), reverse=True)
        final_results = all_results[:limit]

        print(f"🔍 DEBUG: Total results found: {len(all_results)}, returning top {len(final_results)}")

        return final_results

    except Exception as e:
        print(f"❌ Weaviate error: {e}")
        return []

from collections import defaultdict

from collections import defaultdict

LOG_FILE_PATH = "debug_log.txt"  # Change YourUserName accordingly

def log_debug(message: str):
    with open(LOG_FILE_PATH, "a", encoding="utf-8") as f:
        f.write(message + "\n")

def verify_weaviate_embeddings():
    """
    Verify that AreaScanChunks collection has non-empty vector embeddings and metadata.
    """
    try:
        client = weaviate.Client(url="http://localhost:8080")

        print("🔍 DEBUG: Verifying Weaviate embeddings...")

        # Check if AreaScanChunks exists
        schema = client.schema.get()
        available_classes = [cls["class"] for cls in schema.get("classes", [])]

        if AREA_SCAN_CLASS_NAME not in available_classes:
            print(f"❌ Collection '{AREA_SCAN_CLASS_NAME}' not found in Weaviate")
            return False

        # Get count of objects
        count_response = client.query.aggregate(AREA_SCAN_CLASS_NAME).with_meta_count().do()
        count = count_response.get("data", {}).get("Aggregate", {}).get(AREA_SCAN_CLASS_NAME, [{}])[0].get("meta", {}).get("count", 0)
        print(f"🔍 DEBUG: Collection '{AREA_SCAN_CLASS_NAME}' contains {count} objects")

        if count == 0:
            print(f"❌ Collection '{AREA_SCAN_CLASS_NAME}' is empty")
            return False

        # Sample a few objects to check embeddings and metadata
        sample_response = (
            client.query
            .get(AREA_SCAN_CLASS_NAME, ["source_file", "content", "chunk_number"])
            .with_additional(["vector"])
            .with_limit(3)
            .do()
        )

        samples = sample_response.get("data", {}).get("Get", {}).get(AREA_SCAN_CLASS_NAME, [])

        for i, sample in enumerate(samples):
            source_file = sample.get("source_file", "Missing")
            content = sample.get("content", "")
            vector = sample.get("_additional", {}).get("vector", [])

            print(f"🔍 DEBUG: Sample {i+1}:")
            print(f"  - source_file: {source_file}")
            print(f"  - content length: {len(content)} chars")
            print(f"  - vector dimensions: {len(vector)}")
            print(f"  - content preview: '{content[:100]}...'")

            if not source_file or source_file == "Missing":
                print(f"❌ Sample {i+1} missing source_file metadata")

            if not content:
                print(f"❌ Sample {i+1} missing content")

            if not vector or len(vector) == 0:
                print(f"❌ Sample {i+1} missing vector embedding")
                return False

        print("✅ Weaviate embeddings verification passed")
        return True

    except Exception as e:
        print(f"❌ Error verifying Weaviate embeddings: {e}")
        return False

def retrieve_and_generate_answer(query_text, user_id, top_k=5, ticket=None, camera_type=None, certainty_threshold=0.7):
    """
    🔍 Retrieval Behavior:
    - Retrieve top-k chunks from the Weaviate vector store based on semantic similarity to the user query.
    - Each chunk includes metadata such as: source_file, content, chunk_number, camera_type, and certainty.

    📄 Reference File Handling:
    - Track and sum the `certainty` scores for each `source_file` across retrieved chunks.
    - Identify the **single source_file with the highest total certainty score** as the only reference.

    🧠 Answer Generation:
    - Use only the top-k chunk texts to construct the context for GPT answer generation.
    - If no matching document is found, inform the user that no relevant support document exists.
    """
    print(f"🔍 DEBUG: retrieve_and_generate_answer called with certainty_threshold={certainty_threshold}")

    matches = search_similar_chunks_weaviate(query_text, limit=top_k, camera_type=camera_type, certainty_threshold=certainty_threshold)

    print(f"🔍 DEBUG: Retrieved {len(matches)} matches from Weaviate")

    if not matches:
        log_debug("DEBUG ▸ No matches found.")
        print("🔍 DEBUG: No matches found - returning fallback message")
        return {
            "answer": "I couldn't find any relevant support documents for your query. Please try rephrasing your question or contact support directly.",
            "matches": [],
            "file_scores": {},
            "primary_reference_file": None
        }

    # Log retrieved chunks for debugging
    print(f"🔍 DEBUG: Retrieved chunks:")
    for i, match in enumerate(matches):
        certainty = match.get("_additional", {}).get("certainty", 0.0)
        source_file = match.get("source_file", "Unknown")
        content_preview = match.get("content", "")[:100] + "..." if match.get("content") else "No content"
        print(f"  {i+1}. certainty={certainty:.3f}, source={source_file}, content='{content_preview}'")

    context_chunks = [match["content"] for match in matches]
    print(f"🔍 DEBUG: Extracted {len(context_chunks)} context chunks for GPT")

    # Use ticket-specific history if ticket is provided
    history_tuples = get_ticket_chat_history(ticket) if ticket else get_user_history(user_id)

    answer = generate_answer(query_text, context_chunks, history_tuples, ticket=ticket)
    print(f"🔍 DEBUG: Generated answer length: {len(answer)} characters")

    if not ticket:
        add_to_user_history(user_id, query_text, answer)

    # 🔢 Score each file by sum of chunk certainties
    file_scores = defaultdict(float)
    for match in matches:
        source_file = match.get("source_file")
        certainty = match.get("_additional", {}).get("certainty", 0.0)
        if source_file and certainty:
            file_scores[source_file] += certainty

    print(f"🔍 DEBUG: File scores: {dict(file_scores)}")

    # 🏆 Pick the single best file
    primary_reference_file = None
    if file_scores:
        sorted_files = sorted(file_scores.items(), key=lambda x: x[1], reverse=True)
        primary_reference_file = sorted_files[0][0]
        print(f"🔍 DEBUG: Selected top file: {primary_reference_file} with score {sorted_files[0][1]}")
        log_debug(f"DEBUG ▸ Selected top file: {primary_reference_file} with score {sorted_files[0][1]}")
    else:
        print("🔍 DEBUG: No file scores computed.")
        log_debug("DEBUG ▸ No file scores computed.")

    return {
        "answer": answer,
        "matches": matches,
        "file_scores": file_scores,
        "primary_reference_file": primary_reference_file
    }

# === Chat Endpoint (protected) ===

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from chatbot.models import SupportTicket
from collections import Counter



# ── helper --------------------------------------------------------------

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from scipy.spatial.distance import cosine
from collections import Counter

def build_full_query(user_query, product_ctx, problem_description=None, solution_summary=None):
    """
    Build a rich query string by combining user query, product context, problem description, and solution summary.
    
    Args:
        user_query (str): The user's input query.
        product_ctx (dict): Dictionary containing product details (e.g., productType, model).
        problem_description (str, optional): The ticket's problem description.
        solution_summary (str, optional): The ticket's solution summary.
    
    Returns:
        str: A formatted query string with all relevant context.
    """
    ctx_lines = [
        f"Product Type: {product_ctx.get('productType', '')}",
        f"Purchased From: {product_ctx.get('purchasedFrom', '')}",
        f"Year of Purchase: {product_ctx.get('yearOfPurchase', '')}",
        f"Product Name: {product_ctx.get('productName', '')}",
        f"Model: {product_ctx.get('model', '')}",
        f"Serial Number: {product_ctx.get('serialNo', '')}",
        f"Operating System: {product_ctx.get('operatingSystem', '')}",
    ]
    if problem_description:
        ctx_lines.append(f"Problem Description: {problem_description}")
    if solution_summary:
        ctx_lines.append(f"Previous Solution: {solution_summary}")
    context = "\n".join([line for line in ctx_lines if line])
    return f"{context}\n\nUser Query: {user_query}"

from scipy.spatial.distance import cosine

def is_query_related(query_text, ticket, similarity_threshold=0.70):
    """
    Check if the query is related to the ticket's problem description, metadata, and solution summary.
    Returns True if related or if the query is a clarification request, False otherwise.
    """
    if not query_text or not ticket:
        return False

    # List of clarification keywords that are inherently related to the ticket
    clarification_keywords = [
        "elaborate", "more details", "explain", "clarify", "further", "more info",
        "expand", "detail", "describe", "tell me more"
    ]
    if any(keyword in query_text.lower() for keyword in clarification_keywords):
        print(f"DEBUG ▸ Query '{query_text}' identified as a clarification request")
        return True

    # Combine ticket metadata, problem description, and solution summary
    metadata = [
        ticket.product_type or "",
        ticket.purchased_from or "",
        ticket.year_of_purchase or "",
        ticket.brand or "",
        ticket.model_number or "",
        ticket.serial_number or "",
        ticket.operating_system_detailed or "",
        ticket.problem_description or "",
        ticket.solution_summary or "",
    ]
    combined_text = "\n".join([field for field in metadata if field])

    if not combined_text:
        return False

    try:
        query_embedding = get_embedding(query_text)
        ticket_embedding = get_embedding(combined_text)
        similarity = 1 - cosine(query_embedding, ticket_embedding)
        print(f"DEBUG ▸ Query similarity score: {similarity}")
        return similarity >= similarity_threshold
    except Exception as e:
        print(f"Error checking query relevance: {e}")
        return False
    
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from scipy.spatial.distance import cosine

@api_view(["POST"])
@permission_classes([IsAuthenticated])
def chat(request):
    """
    POST body expected:
    {
        "query"          : "user text",
        "ticket_id"      : "TCKT-123...",
        "ticket_mode"    : true | false,
        "stage"          : "await_close" | "unrelated_query" | "create_new_ticket" | "",
        "product_context": { ... }            # optional – keys see build_full_query()
    }
    """
    user = request.user
    query = (request.data.get("query") or "").strip()
    stage = request.data.get("stage", "")
    tmode = bool(request.data.get("ticket_mode", False))
    tid = request.data.get("ticket_id")
    pctx = request.data.get("product_context") or {}

    if not query:
        return Response({"error": "Query is required"}, status=400)

    print(f"\n=== DEBUG ▸ New chat call – user:{user.id} ticket_mode:{tmode} stage:{stage} query:{query}")

    # ── ticket fetch (if ticket_mode) ────────────────────────────────────
    ticket = None
    if tmode and tid:
        try:
            ticket = SupportTicket.objects.get(ticket_number=tid, user=user)
            print("DEBUG ▸ Ticket found:", ticket.ticket_number)

            # Check if this is the first message and ticket needs problem description
            if not ticket.problem_description:
                print(f"🎯 DEBUG ▸ FIRST MESSAGE: Collecting problem description for ticket {ticket.ticket_number}")
                # This is the problem description - save it and generate initial prompt
                ticket.problem_description = query.strip()

                # Generate problem summary
                try:
                    problem_summary = generate_gpt_summary(
                        ticket.problem_description,
                        "Summarize this problem description clearly and professionally:"
                    )
                    if problem_summary:
                        ticket.problem_summary = problem_summary
                except Exception as e:
                    print(f"❌ Error generating problem summary: {e}")

                ticket.save(update_fields=['problem_description', 'problem_summary'])
                print(f"🎯 DEBUG ▸ ✅ Problem description saved for ticket {ticket.ticket_number}")

                # Generate initial prompt with document context
                try:
                    matches = search_similar_chunks_weaviate(query, limit=5)
                    context_chunks = [m["content"] for m in matches] if matches else []

                    # Generate and save the initial prompt
                    get_or_generate_ticket_prompt(
                        ticket=ticket,
                        user_description=query,
                        context_chunks=context_chunks,
                        force_regenerate=True
                    )
                    print(f"🎯 DEBUG ▸ ✅ Initial prompt generated for ticket {ticket.ticket_number}")
                except Exception as e:
                    print(f"❌ Error generating initial prompt: {e}")
            else:
                print(f"🎯 DEBUG ▸ FOLLOW-UP MESSAGE: Using existing ticket prompt for {ticket.ticket_number}")

        except SupportTicket.DoesNotExist:
            print("DEBUG ▸ Ticket NOT found:", tid)
            return Response({"error": "Invalid ticket ID"}, status=400)

    # ── handle unrelated query response ──────────────────────────────────
    if stage == "unrelated_query" and ticket:
        low = query.lower()
        if low == "yes":
            # User wants to create a new ticket
            return Response({
                "answer": "Alright, let's create a new support ticket. Please provide the product type.",
                "ticket_status": ticket.status,
                "stage": "create_new_ticket",
                "files": [],
            })
        elif low == "no":
            # Ask if the user wants to close the current ticket
            return Response({
                "answer": "Okay, do you want to close the current ticket? (yes/no)",
                "ticket_status": ticket.status,
                "stage": "await_close",
                "files": [],
            })
        else:
            return Response({
                "answer": "Please answer 'yes' or 'no'. Do you want to create a new ticket?",
                "ticket_status": ticket.status,
                "stage": "unrelated_query",
                "files": [],
            })

    # ── close / escalate branch ──────────────────────────────────────────
    if stage == "await_close" and ticket:
        low = query.lower()
        if "escalate" in low:
            ticket.status = "escalated"
            ticket.save(update_fields=["status"])
            return Response({
                "answer": "Your ticket has been escalated. The technical support team will contact you ASAP.",
                "ticket_status": "escalated",
                "stage": "",
                "files": [],
            })
        if low in {"yes", "y", "close"}:
            ticket.status = "closed"
            ticket.save(update_fields=["status"])
            return Response({
                "answer": f"✅ Ticket {ticket.ticket_number} has been closed. Thank you!",
                "ticket_status": "closed",
                "stage": "",
                "files": [],
            })
        return Response({
            "answer": "Okay, ticket will remain open.",
            "ticket_status": "open",
            "stage": "",
            "files": [],
        })

    # ── build a single rich query string ─────────────────────────────────
    if ticket:
        db_ctx = {
            "productType":     ticket.product_type,
            "purchasedFrom":   ticket.purchased_from,
            "yearOfPurchase":  ticket.year_of_purchase,
            "brand":           ticket.brand,
            "modelNumber":     ticket.model_number,
            "serialNumber":    ticket.serial_number,
            "operatingSystem": ticket.operating_system_detailed,
        }
        full_query = build_full_query(
            user_query=query,
            product_ctx=db_ctx,
            problem_description=ticket.problem_description,
            solution_summary=ticket.solution_summary or "",  # Include solution_summary
        )
    else:
        full_query = build_full_query(
            user_query=query,
            product_ctx=pctx,
            problem_description=pctx.get("problemDescription"),
        )

    # ── check if query is related to ticket (in ticket mode, after problem description) ─────────────
    if tmode and ticket and ticket.problem_description and query != "Please help me with this issue":
        print(f"DEBUG ▸ Checking if query '{query}' is related to ticket {ticket.ticket_number}")
        print(f"DEBUG ▸ Ticket problem description: {ticket.problem_description[:100]}...")
        if not is_query_related(query, ticket):
            print("DEBUG ▸ Query unrelated to ticket:", ticket.ticket_number)
            return Response({
                "answer": f"Your query seems unrelated to ticket {ticket.ticket_number}. Would you like to create a new ticket for this issue?",
                "ticket_status": ticket.status,
                "stage": "unrelated_query",
                "files": [],
            })
        else:
            print("DEBUG ▸ Query is related to ticket, proceeding with normal processing")

    # ── cache check ──────────────────────────────────────────────────────
    if not tmode:
        print("DEBUG ▸ Ticket mode is OFF - running cache check…")
        vec = get_embedding(full_query)
        cached = search_cache(vec, certainty_threshold=0.95)
        if cached:
            print("DEBUG ▸ Cache HIT - returning cached answer")
            return Response({
                "query": query,
                "answer": cached["answer"],
                "source": "cache",
                "cached_query": cached["query_text"],
                "matches": [],
                "files": cached["files"],
                "stage": "",
            })
        print("DEBUG ▸ Cache MISS - no cached answer found")
    else:
        print("DEBUG ▸ Ticket mode is ON - skipping cache check and querying GPT fresh")

    # ── retrieval + LLM answer ───────────────────────────────────────────
    print("DEBUG ▸ Querying Weaviate for retrieval chunks…")

    # Extract camera type from product context for targeted search
    camera_type = extract_camera_type_from_context(pctx)
    if camera_type:
        print(f"DEBUG ▸ Detected camera type: {camera_type}")
    else:
        print("DEBUG ▸ No specific camera type detected, searching all categories")

    # Use the updated retrieve_and_generate_answer function
    result = retrieve_and_generate_answer(full_query, user_id=user.id, top_k=5, ticket=ticket, camera_type=camera_type)
    answer = result.get("answer", "No answer available.")
    matches = result.get("matches", [])
    file_scores = result.get("file_scores", {})
    primary_reference_file = result.get("primary_reference_file")

    print(f"DEBUG ▸ {len(matches)} chunks retrieved")
    print(f"🎯 DEBUG ▸ GPT answer generated (length): {len(answer)} characters")

    # 📎 Download Offer Logic: Create file objects based on primary reference file
    # Map source_file names to actual database entries to get correct download links
    file_objs = []
    if primary_reference_file:
        # Get unique source files from all retrieved chunks to avoid duplicates
        unique_source_files = set()
        for match in matches:
            source_file = match.get("source_file")
            if source_file and source_file in file_scores:
                unique_source_files.add(source_file)

        # Map each unique source file to database entry
        for source_filename in unique_source_files:
            try:
                # Look up the file in the database by filename
                pdf_file = PdfFile.objects.get(file_name=source_filename)
                file_objs.append({
                    "filename": source_filename,
                    "url": f"/api/download-pdf/{pdf_file.id}/",  # Use new ID-based endpoint
                    "pdf_id": pdf_file.id,
                    "score": file_scores.get(source_filename, 0.0)
                })
                print(f"🔍 DEBUG: Mapped {source_filename} to database ID {pdf_file.id} -> /api/download-pdf/{pdf_file.id}/")
            except PdfFile.DoesNotExist:
                print(f"⚠️ WARNING: Source file '{source_filename}' not found in database - skipping download offer")
                continue

        # Sort by score (highest first) to show most relevant files first
        file_objs.sort(key=lambda x: x['score'], reverse=True)

    # ── save two-line summary (ticket mode) ──────────────────────────────
    if ticket:
        try:
            summ_src = answer if answer and len(answer.strip()) >= 10 else f"Brief answer:\n{answer}"
            summ = generate_gpt_summary(summ_src, "Summarize in 2 lines:")
            ticket.solution_summary = (summ or "No solution yet.")
            ticket.save(update_fields=["solution_summary"])
            print(f"✅ Solution summary saved for ticket {ticket.ticket_number}")
        except Exception as e:
            print("DEBUG ▸ summary save error:", e)

    # ── Enhanced follow-up handling with download offer logic ───────────────────────────────────────────────
    follow_up = ""
    next_stage = ""

    # 📎 Download Offer Logic: After answering, append download offer if reference file exists
    if file_objs:
        follow_up = "\n\n💡 Would you like to download the reference document used to answer this question?"
    else:
        follow_up = ""
   

    # ── final response ───────────────────────────────────────────────────
    return Response({
        "query": query,
        "answer": answer + follow_up,
        "matches": matches,
        "files": file_objs,
        "source": "gpt",
        "stage": next_stage,
        "ticket_context": {
            "ticket_number": ticket.ticket_number if ticket else None,
            "product_info": f"{ticket.brand} {ticket.product_type} - {ticket.model_number}" if ticket and ticket.brand and ticket.model_number else f"{ticket.product_type} - {ticket.model_number}" if ticket and ticket.model_number else None,
        } if ticket else None,
    })




from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response
from .models import SupportTicket
from .serializers import (
    EscalatedTicketListSerializer,
    EscalatedTicketDetailSerializer,
)
@api_view(["GET"])
@permission_classes([IsAdminUser])
def escalated_tickets(request):
    qs = SupportTicket.objects.filter(status="escalated").order_by("-created_at")
    data = EscalatedTicketListSerializer(qs, many=True).data
    return Response(data)


@api_view(["GET"])
@permission_classes([IsAdminUser])
def escalated_ticket_detail(request, ticket_number):
    try:
        ticket = SupportTicket.objects.get(ticket_number=ticket_number)
    except SupportTicket.DoesNotExist:
        return Response({"detail": "Ticket not found."}, status=404)
    data = EscalatedTicketDetailSerializer(ticket).data
    return Response(data)



import os
import subprocess
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response

@api_view(["POST"])
@permission_classes([IsAdminUser])
def run_processing_pipeline(request):
    """
    Processing pipeline:
    1. Chunk PDFs (chunking.py)
    2. Push vectors to Weaviate (vector_embedding.py)
    All scripts must be located inside the 'chatbot' folder.
    """

    # Get absolute path to your Django project root
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    chatbot_dir = os.path.join(BASE_DIR, "chatbot")

    # Use current Python interpreter (works with any environment)
    import sys
    venv_python = sys.executable

    # Define scripts in execution order
    scripts = [
        # {"name": "img2.py", "args": []},
        {"name": "chunking.py", "args": []},
        {"name": "vector_embedding.py", "args": ["--weaviate"]},
    ]

    for script in scripts:
        script_name = script["name"]
        script_args = script["args"]
        script_path = os.path.join(chatbot_dir, script_name)

        if os.path.exists(script_path):
            try:
                command = [venv_python, script_path] + script_args
                result = subprocess.run(
                    command,
                    capture_output=True,
                    text=True,
                    check=True
                )
                print(f"✅ Script {script_name} completed:\n{result.stdout}")
            except subprocess.CalledProcessError as e:
                print(f"❌ Script {script_name} failed:\n{e.stderr}")
                return Response(
                    {"detail": f"Script {script_name} failed", "error": e.stderr},
                    status=500
                )
        else:
            print(f"❌ Script not found: {script_path}")
            return Response(
                {"detail": f"Script not found: {script_name}"},
                status=400
            )

    return Response({"detail": "Processing pipeline completed."}, status=200)



# === Health Check ===

@api_view(['GET'])
@permission_classes([AllowAny])
def health(request):
    weaviate_status = "unavailable"
    collections = []
    counts = {}

    try:
        client = weaviate.Client(url="http://localhost:8080")
        schema = client.schema.get()
        if 'classes' in schema:
            collections = [cls['class'] for cls in schema['classes']]
            for collection in collections:
                result = client.query.aggregate(collection).with_meta_count().do()
                if result and "data" in result and "Aggregate" in result["data"]:
                    count = result["data"]["Aggregate"][collection][0]["meta"]["count"]
                    counts[collection] = count
            weaviate_status = "connected"
        client.close()
    except Exception as e:
        weaviate_status = f"error: {str(e)}"

    return Response({
        "status": "healthy",
        "weaviate_status": weaviate_status,
        "collections": collections,
        "counts": counts
    })


# === Home API ===

@api_view(['GET'])
@permission_classes([AllowAny])
def home(request):
    return Response({
        "message": "AI Agent Chatbot Backend API with RAG (Django)",
        "version": "2.0",
        "framework": "Django + Django REST Framework",
        "endpoints": {
            "/api/chat/": "POST - AI-powered chat with context (RAG) [Requires Authentication]",
            "/api/signup/": "POST - Signup new user",
            "/api/token/": "POST - Login (JWT token obtain)",
            "/api/token/refresh/": "POST - Refresh JWT token",
            "/api/health/": "GET - Health check",
            "/api/upload_pdf/": "POST - Upload PDF files"
        },
        "features": [
            "JWT Authentication",
            "GPT-4o-mini powered answers",
            "Retrieval Augmented Generation (RAG)",
            "Weaviate vector database integration",
            "Technical documentation expertise"
        ]
    })